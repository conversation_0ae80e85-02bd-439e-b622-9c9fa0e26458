const fs = require('fs');

console.log('🔗 Generátor Google Maps URL s předvyplněnými trasami');
console.log('===================================================\n');

try {
    // Načtení detailních adres
    const data = JSON.parse(fs.readFileSync('extracted-addresses-google-maps.json', 'utf8'));
    const addresses = data.addresses;
    
    console.log(`📊 Celkem adres: ${addresses.length}`);
    
    // Vytvoření tras po 8 adresách
    const routeSize = 8;
    const routes = [];
    
    for (let i = 0; i < Math.min(addresses.length, 24); i += routeSize) {
        const routeAddresses = addresses.slice(i, i + routeSize);
        const routeNumber = Math.floor(i / routeSize) + 1;
        
        console.log(`\n🗺️ Vytváření URL pro trasu ${routeNumber}...`);
        
        // Vytvoření Google Maps URL
        const baseUrl = 'https://www.google.com/maps/dir/';
        
        // Kódování adres pro URL
        const encodedAddresses = routeAddresses.map(addr => {
            // Použijeme jen základní adresu bez jmen pro lepší rozpoznání
            const simpleAddress = `${addr.street}, ${addr.city}, ${addr.postCode}`;
            return encodeURIComponent(simpleAddress);
        });
        
        const googleMapsUrl = baseUrl + encodedAddresses.join('/');
        
        // Vytvoření zkráceného URL (Google Maps automaticky zkrátí)
        const routeInfo = {
            id: routeNumber,
            name: `Google Maps Trasa ${routeNumber}`,
            addressCount: routeAddresses.length,
            url: googleMapsUrl,
            addresses: routeAddresses.map(addr => ({
                name: addr.googleMapsLabel,
                address: addr.address,
                phone: addr.phone,
                fullText: addr.fullGoogleMapsText
            }))
        };
        
        routes.push(routeInfo);
        
        console.log(`✅ URL vytvořeno pro ${routeAddresses.length} adres`);
        console.log(`📍 První adresa: ${routeAddresses[0].googleMapsLabel}`);
        console.log(`📍 Poslední adresa: ${routeAddresses[routeAddresses.length - 1].googleMapsLabel}`);
        
        // Vytvoření souboru s URL a instrukcemi
        const urlContent = [
            `🗺️ GOOGLE MAPS TRASA ${routeNumber} - PŘÍMÝ ODKAZ`,
            `Počet zastávek: ${routeAddresses.length}`,
            `Vygenerováno: ${new Date().toLocaleString('cs-CZ')}`,
            '='.repeat(70),
            '',
            '🔗 PŘÍMÝ ODKAZ (klikněte nebo zkopírujte):',
            googleMapsUrl,
            '',
            '📋 INSTRUKCE:',
            '1. Klikněte na odkaz výše',
            '2. Google Maps se otevře s předvyplněnou trasou',
            '3. Klikněte na "Optimalizovat pořadí zastávek" (pokud je dostupné)',
            '4. Upravte výchozí bod podle potřeby',
            '5. Uložte trasu do svého Google účtu',
            '',
            '⚠️ POZNÁMKA:',
            'Pokud se některé adresy nenačtou správně, použijte ruční přidání',
            'z detailního seznamu níže.',
            '',
            '='.repeat(70),
            'DETAILNÍ SEZNAM PRO RUČNÍ PŘIDÁNÍ:',
            '',
            ...routeAddresses.map((addr, index) => {
                let info = `${index + 1}. ${addr.googleMapsLabel}\n   📍 ${addr.address}`;
                if (addr.phone) {
                    info += `\n   📞 ${addr.phone}`;
                }
                info += `\n   🗺️ Pro Google Maps: "${addr.fullGoogleMapsText}"`;
                return info;
            }),
            '',
            '='.repeat(70),
            'ALTERNATIVNÍ RUČNÍ POSTUP:',
            '1. Otevřete https://maps.google.com',
            '2. Klikněte na "Trasy"',
            '3. Postupně přidejte adresy z detailního seznamu výše',
            '4. Použijte text z řádku "🗺️ Pro Google Maps:"',
            '5. Aktivujte optimalizaci trasy',
            '',
            `Konec trasy ${routeNumber}`
        ].join('\n');
        
        fs.writeFileSync(`google-maps-url-trasa-${routeNumber}.txt`, urlContent, { encoding: 'utf8' });
    }
    
    // Uložení všech URL do jednoho souboru
    const allUrlsContent = [
        '🔗 VŠECHNY GOOGLE MAPS TRASY - PŘÍMÉ ODKAZY',
        `Celkem tras: ${routes.length}`,
        `Vygenerováno: ${new Date().toLocaleString('cs-CZ')}`,
        '='.repeat(70),
        '',
        ...routes.map(route => [
            `🗺️ TRASA ${route.id} (${route.addressCount} zastávek):`,
            route.url,
            `📍 ${route.addresses[0].name} → ${route.addresses[route.addresses.length - 1].name}`,
            ''
        ].join('\n')),
        '='.repeat(70),
        'INSTRUKCE PRO POUŽITÍ:',
        '1. Zkopírujte URL trasy, kterou chcete použít',
        '2. Vložte do prohlížeče nebo klikněte na odkaz',
        '3. Google Maps se otevře s předvyplněnou trasou',
        '4. Zkontrolujte a optimalizujte podle potřeby',
        '5. Uložte do svého Google účtu pro pozdější použití',
        '',
        'Pro detailní instrukce viz soubory google-maps-url-trasa-X.txt'
    ].join('\n');
    
    fs.writeFileSync('google-maps-all-urls.txt', allUrlsContent, { encoding: 'utf8' });
    
    // Uložení do JSON pro programové použití
    fs.writeFileSync('google-maps-urls.json', JSON.stringify({ routes }, null, 2), { encoding: 'utf8' });
    
    console.log('\n🎯 HOTOVO!');
    console.log('=====================================');
    console.log(`✅ Vytvořeno ${routes.length} Google Maps URL`);
    console.log('📁 Vytvořené soubory:');
    console.log(`• google-maps-url-trasa-1.txt až google-maps-url-trasa-${routes.length}.txt`);
    console.log('• google-maps-all-urls.txt - všechny odkazy');
    console.log('• google-maps-urls.json - data pro programy');
    
    console.log('\n🚀 Jak použít:');
    console.log('1. Otevřete soubor google-maps-url-trasa-X.txt');
    console.log('2. Klikněte na URL odkaz');
    console.log('3. Google Maps se otevře s předvyplněnou trasou');
    console.log('4. Optimalizujte a uložte trasu');
    
    console.log('\n📋 Ukázka URL pro trasu 1:');
    if (routes.length > 0) {
        console.log(routes[0].url.substring(0, 100) + '...');
    }
    
} catch (error) {
    console.error('❌ Chyba při vytváření URL:', error.message);
    console.log('\n💡 Ujistěte se, že existuje soubor extracted-addresses-google-maps.json');
    console.log('Spusťte nejdříve: node extract-detailed-addresses.js');
}
