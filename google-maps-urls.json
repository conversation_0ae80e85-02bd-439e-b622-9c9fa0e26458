{"routes": [{"id": 1, "name": "Google Maps Trasa 1", "addressCount": 8, "url": "https://www.google.com/maps/dir/Chel%C4%8Dice%205%2C%20Chel%C4%8Dice%2C%20389%2001/Jin%C3%ADn%2055%2C%20Jin%C3%ADn%2C%20386%2001/Lib%C4%9Bjovice%2084%2C%20Lib%C4%9Bjovice%2C%20387%2072/46%2C%20Strakonice%2C%20386%2001/Bahenn%C3%AD%201398%2C%20Strakonice%2C%20386%2001/Bo%C5%BEeny%20N%C4%9Bmcov%C3%A9%201117%2C%20Strakonice%2C%20386%2001/Dra%C5%BEejov90%2C%20Strakonice%2C%20386%2001/Dukelsk%C3%A1%2033%20%2C%20Strakonice%2C%20386%2001", "addresses": [{"name": "OX BOX Chelčice - olga nová", "address": "Chelčice 5, <PERSON><PERSON><PERSON><PERSON>, 389 01", "phone": "+420777862314", "fullText": "OX BOX Chelčice - olga nová, Chelčice 5, <PERSON><PERSON><PERSON><PERSON>, 389 01"}, {"name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON> 55, <PERSON><PERSON>, 386 01", "phone": "+420722960264", "fullText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> 55, <PERSON><PERSON>, 386 01"}, {"name": "Pazdera Jiří - <PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 84, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 72", "phone": "00420605297185", "fullText": "Pazdera Jiří - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 84, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 72"}, {"name": "<PERSON><PERSON><PERSON>", "address": "46, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420723539104", "fullText": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, 46, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "<PERSON><PERSON><PERSON><PERSON>lov<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1398, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "777611022", "fullText": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1398, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "<PERSON> - Ho<PERSON>ová", "address": "<PERSON><PERSON><PERSON><PERSON> Ně<PERSON>v<PERSON> 1117, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420606636880", "fullText": "<PERSON> - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Ně<PERSON> 1117, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420734357069", "fullText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "CZ004CST - <PERSON><PERSON>", "address": "Dukels<PERSON>á 33, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420607065695", "fullText": "CZ004CST - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 33, <PERSON><PERSON><PERSON><PERSON>, 386 01"}]}, {"id": 2, "name": "Google Maps Trasa 2", "addressCount": 8, "url": "https://www.google.com/maps/dir/Chmelensk%C3%A9ho%2C%20167%2C%20Strakonice%2C%20386%2001/Katovick%C3%A1%201268%20-%20IC%2C%20Strakonice%2C%20386%2001/Katovick%C3%A1%201307%2C%20Strakonice%2C%20386%2001/Katovick%C3%A1%201404%2C%20Strakonice%2C%20386%2001/Kochana%20z%20Prachov%C3%A9119%2C%20Strakonice%2C%20386%2001/Lu%C4%8Dn%C3%AD%20448%2C%20Strakonice%2C%20386%2001/M%C3%ADrov%C3%A1%201010%20%2C%20Strakonice%2C%20386%2001/Na%20Ohrad%C4%9B%202%2C%20Strakonice%2C%20386%2001", "addresses": [{"name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 167, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "00420734511135", "fullText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 167, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "PTÁČEK - velkoobchod, a.s.", "address": "Katovick<PERSON> 1268 - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "", "fullText": "PTÁČEK - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a.s., Katovická 1268 - IC, Strak<PERSON>e, 386 01"}, {"name": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1307, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420606843920", "fullText": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1307, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "AlzaBox Katovická - Lenka Slavíčková", "address": "<PERSON><PERSON><PERSON><PERSON> 1404, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "420739755746", "fullText": "AlzaBox Katovická - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1404, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "<PERSON><PERSON><PERSON>", "address": "Kochana z Prachové119, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420722596652", "fullText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>119, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "¼uboš Bandry", "address": "<PERSON><PERSON><PERSON><PERSON> 448, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420723785332", "fullText": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 448, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "CZ003CST - <PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1010, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420606732965", "fullText": "CZ003CST - <PERSON>, <PERSON><PERSON><PERSON><PERSON> 1010, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "AlzaBox Na Ohradě - <PERSON>", "address": "<PERSON> Ohradě 2, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420607783283", "fullText": "AlzaBox Na Ohradě - <PERSON>, <PERSON> 2, <PERSON><PERSON><PERSON><PERSON>, 386 01"}]}, {"id": 3, "name": "Google Maps Trasa 3", "addressCount": 8, "url": "https://www.google.com/maps/dir/P%C3%ADseck%C3%A1%20%2C%20Strakonice%2C%20386%2001/Rado%C5%A1ovice%20%2042%2C%20Strakonice%2C%20386%2001/%C5%98epice%20136%2C%20Strakonice%2C%20386%2001/Smetanova%20883%2C%20Strakonice%2C%20386%2001/Smidingerova%20795%2C%20Strakonice%2C%20386%2001/Stavba%C5%99%C5%AF207%2C%20Strakonice%2C%20386%2001/%C5%A0t%C4%9Bke%C5%88-Rechle%20194%2C%20Strakonice%2C%20386%2001/Tr%C5%BEn%C3%AD1152%2C%20Strakonice%2C%20386%2001", "addresses": [{"name": "AlzaBox Písecká - <PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON>ecká, Strakonice, 386 01", "phone": "+420774343499", "fullText": "AlzaBox Písecká - <PERSON><PERSON><PERSON><PERSON>, P<PERSON>eck<PERSON>, Strakonice, 386 01"}, {"name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "address": "Radošovice  42, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "", "fullText": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>  42, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "ELEKTRO S.M.S., spol. s r.o.Strakonice", "address": "Řepice 136, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420383322474", "fullText": "ELEKTRO S.M.S., spol. s r.<PERSON><PERSON>, Řepice 136, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "AlzaBox Smetanova - <PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 883, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420736162719", "fullText": "AlzaBox Smetanova - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 883, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "Ondř<PERSON>,", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 795, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420722727888", "fullText": "<PERSON><PERSON><PERSON><PERSON>,, <PERSON><PERSON><PERSON><PERSON><PERSON> 795, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>207, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420605260940", "fullText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>207, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "<PERSON>,", "address": "Štěkeň-<PERSON><PERSON><PERSON> 194, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420606748986", "fullText": "<PERSON>,, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 194, <PERSON><PERSON><PERSON><PERSON>, 386 01"}, {"name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>1152, <PERSON><PERSON><PERSON><PERSON>, 386 01", "phone": "+420604302441", "fullText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>1152, <PERSON><PERSON><PERSON><PERSON>, 386 01"}]}]}