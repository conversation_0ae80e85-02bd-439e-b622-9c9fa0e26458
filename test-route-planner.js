const { chromium } = require('playwright');

// Nastavení <PERSON> pro správné zobrazení češtiny
process.stdout.setEncoding('utf8');

(async () => {
    console.log('🧪 Testovací plánovač tras - 5 adres');
    console.log('=====================================\n');

    // Testovací adresy z první trasy
    const testAddresses = [
        { name: '<PERSON><PERSON><PERSON>', address: '46, Strakonice, 386 01' },
        { name: '<PERSON><PERSON><PERSON><PERSON>', address: 'Bahenní 1398, Strakonice, 386 01' },
        { name: '<PERSON>', address: 'Boženy Nímcové 1117, Strakonice, 386 01' },
        { name: '<PERSON><PERSON><PERSON>', address: 'Dražejov90, Strakonice, 386 01' },
        { name: 'CZ004CST', address: 'Dukelská 33, Strakonice, 386 01' }
    ];

    console.log(`📍 Testovací adresy (${testAddresses.length}):`);
    testAddresses.forEach((addr, i) => {
        console.log(`   ${i + 1}. ${addr.name} - ${addr.address}`);
    });

    console.log('\n🌐 Spouštím prohlížeč a otevírám mapy.cz...');

    // Spuštění prohlížeče
    const browser = await chromium.launch({
        headless: false,  // Zobrazíme prohlížeč
        slowMo: 500      // Zpomalíme pro lepší sledování
    });

    const context = await browser.newContext();
    const page = await context.newPage();

    try {
        // Otevření mapy.cz
        console.log('📍 Otevírám mapy.cz...');
        await page.goto('https://mapy.cz', { waitUntil: 'networkidle' });

        // Počkáme na načtení stránky
        await page.waitForTimeout(3000);

        // Zkusíme zavřít případné cookies dialog
        try {
            const cookiesButton = page.locator('button:has-text("Souhlasím")');
            if (await cookiesButton.count() > 0) {
                await cookiesButton.click();
                console.log('✅ Cookies dialog zavřen');
                await page.waitForTimeout(1000);
            }
        } catch (e) {
            console.log('ℹ️ Cookies dialog nenalezen nebo již zavřen');
        }

        // Klikneme na tlačítko pro plánování trasy
        console.log('🗺️ Hledám tlačítko pro plánování trasy...');

        try {
            const routeSelectors = [
                'button[title*="trasa"]',
                'button[title*="Trasa"]',
                'button:has-text("Trasa")',
                '[data-testid*="route"]',
                '.route-button',
                'button[aria-label*="trasa"]'
            ];

            let routeButton = null;
            for (const selector of routeSelectors) {
                try {
                    routeButton = page.locator(selector).first();
                    if (await routeButton.count() > 0) {
                        console.log(`✅ Nalezeno tlačítko trasy: ${selector}`);
                        await routeButton.click();
                        console.log('✅ Kliknuto na tlačítko trasy');
                        await page.waitForTimeout(2000);
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }

        } catch (error) {
            console.log('⚠️ Problém s tlačítkem trasy, zkusím alternativní přístup');
            await page.goto('https://mapy.cz/zakladni?planovac-tras=true');
            await page.waitForTimeout(3000);
        }

        console.log('\n📍 Začínám přidávat testovací adresy...');

        // Přidání adres do plánovače tras
        for (let i = 0; i < testAddresses.length; i++) {
            const addr = testAddresses[i];
            console.log(`\n   ${i + 1}/${testAddresses.length}: Přidávám ${addr.name}`);
            console.log(`   📍 Adresa: ${addr.address}`);

            try {
                // Pro 3. adresu a každou další musíme nejdříve kliknout na tlačítko "+"
                if (i >= 2) {
                    console.log(`     🔘 Hledám tlačítko + pro přidání nového input pole...`);

                    // Nejdříve zkusíme najít všechna tlačítka na stránce
                    console.log(`     🔍 Debug: Hledám všechna tlačítka na stránce...`);
                    const allButtons = page.locator('button');
                    const buttonCount = await allButtons.count();
                    console.log(`     📊 Celkem tlačítek na stránce: ${buttonCount}`);

                    // Zkusíme najít tlačítka s textem "+"
                    for (let j = 0; j < Math.min(buttonCount, 20); j++) {
                        try {
                            const button = allButtons.nth(j);
                            const buttonText = await button.textContent();
                            const buttonClass = await button.getAttribute('class');
                            const buttonTitle = await button.getAttribute('title');

                            if (buttonText && (buttonText.includes('+') || buttonText.includes('přidat'))) {
                                console.log(`     🎯 Nalezeno tlačítko s "+": text="${buttonText}", class="${buttonClass}"`);
                                try {
                                    if (await button.isVisible()) {
                                        await button.click();
                                        console.log(`     ✅ Kliknuto na tlačítko s "+" textem`);
                                        await page.waitForTimeout(1500);
                                        break;
                                    }
                                } catch (e) {
                                    console.log(`     ⚠️ Chyba při klikání: ${e.message}`);
                                }
                            }
                        } catch (e) {
                            continue;
                        }
                    }

                    // Zkusíme také obecné selektory
                    const plusButtonSelectors = [
                        'div.route-item-plus button',
                        '.route-item-plus button.plus',
                        '.route-item-plus .plus',
                        'button.plus',
                        '.plus',
                        '[class*="plus"]',
                        'button:has-text("+")',
                        '[title*="přidat"]',
                        '[aria-label*="přidat"]',
                        '.route-item-plus > button',
                        'button[class*="add"]',
                        'button[class*="Add"]',
                        '[role="button"]:has-text("+")',
                        'div[class*="route"] button',
                        'button:visible'
                    ];

                    let plusButtonFound = false;
                    for (const selector of plusButtonSelectors) {
                        try {
                            console.log(`       Zkouším selektor: ${selector}`);
                            const plusButtons = page.locator(selector);
                            const selectorButtonCount = await plusButtons.count();
                            console.log(`       Nalezeno ${selectorButtonCount} tlačítek`);

                            if (selectorButtonCount > 0) {
                                // Zkusíme najít viditelné tlačítko
                                for (let j = 0; j < selectorButtonCount; j++) {
                                    const button = plusButtons.nth(j);
                                    if (await button.isVisible()) {
                                        const buttonText = await button.textContent();
                                        const buttonClass = await button.getAttribute('class');
                                        console.log(`       Tlačítko ${j}: text="${buttonText}", class="${buttonClass}"`);

                                        if (buttonText && buttonText.includes('+')) {
                                            console.log(`       Klikám na tlačítko ${j + 1} s "+"...`);
                                            await button.click();
                                            console.log(`     ✅ Kliknuto na tlačítko + (${selector})`);
                                            plusButtonFound = true;
                                            await page.waitForTimeout(1500);
                                            break;
                                        }
                                    }
                                }
                                if (plusButtonFound) break;
                            }
                        } catch (e) {
                            console.log(`       Chyba s ${selector}: ${e.message}`);
                            continue;
                        }
                    }

                    if (!plusButtonFound) {
                        console.log(`     ⚠️ Tlačítko + nenalezeno, zkusím pokračovat...`);
                        // Zkusíme počkat a hledat znovu
                        await page.waitForTimeout(2000);
                    }
                }

                // Hledáme input pole pro zadání adresy
                console.log(`     🔍 Hledám input pole pro adresu...`);
                const inputSelectors = [
                    'input[placeholder*="adres"]',
                    'input[placeholder*="místo"]',
                    'input[placeholder*="hled"]',
                    'input[type="text"]',
                    '.search-input input',
                    '[data-testid*="search"] input',
                    '.route-input input',
                    '[class*="route"] input'
                ];

                let addressInput = null;
                for (const selector of inputSelectors) {
                    try {
                        const inputs = page.locator(selector);
                        const inputCount = await inputs.count();
                        console.log(`       ${selector}: ${inputCount} inputů`);

                        if (inputCount > 0) {
                            // Pro první dvě adresy použijeme první dva inputy
                            // Pro další adresy použijeme poslední (nejnovější) input
                            let targetInput;
                            if (i < 2) {
                                targetInput = inputs.nth(i);
                                console.log(`       Používám input ${i} (první dva)`);
                            } else {
                                targetInput = inputs.last();
                                console.log(`       Používám poslední input`);
                            }

                            if (await targetInput.isVisible()) {
                                addressInput = targetInput;
                                console.log(`     🎯 Nalezen input (${selector})`);
                                break;
                            }
                        }
                    } catch (e) {
                        continue;
                    }
                }

                if (addressInput) {
                    console.log(`     ⌨️ Vyplňujem adresu...`);
                    await addressInput.click();
                    await page.waitForTimeout(500);
                    await addressInput.fill(addr.address);
                    await page.waitForTimeout(1000);
                    await page.keyboard.press('Enter');
                    await page.waitForTimeout(3000);

                    console.log(`     ✅ Adresa "${addr.address}" přidána`);
                } else {
                    console.log(`     ❌ Input pole nenalezeno pro adresu: ${addr.address}`);
                }

            } catch (error) {
                console.log(`     ❌ Chyba při přidávání adresy ${addr.address}:`, error.message);
            }
        }

        console.log('\n🎯 Test dokončen! Zkontrolujte výsledek v prohlížeči.');
        console.log('⏳ Prohlížeč zůstane otevřený. Stiskněte Ctrl+C pro ukončení...');

        // Čekáme nekonečně dlouho, dokud uživatel neukončí
        await new Promise(() => {});

    } catch (error) {
        console.error('❌ Došlo k chybě při testování:', error.message);
    } finally {
        console.log('🔚 Zavírám prohlížeč...');
        await browser.close();
    }
})();
