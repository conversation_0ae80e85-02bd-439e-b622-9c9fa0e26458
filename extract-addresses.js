const XLSX = require('xlsx');
const fs = require('fs');

// Nastavení kódování pro správné zobrazení češtiny
process.stdout.setEncoding('utf8');

// Funkce pro opravu kódování českých znaků
function fixCzechEncoding(text) {
    if (!text || typeof text !== 'string') return text;

    // Nejdříve zkontrolujeme, jestli text už není v UTF-8
    try {
        // Pokud text obsahuje správné česk<PERSON> znaky, neměníme ho
        if (/[áčďéěíňóřšťúůýž]/i.test(text)) {
            return text;
        }
    } catch (e) {
        // Pokračujeme s opravou
    }

    // Specifické opravy pro časté chyby v kódování
    const replacements = {
        // Základní diakritika - malá písmena
        'Ã¡': 'á', 'Ã©': 'é', 'Ã­': 'í', 'Ã³': 'ó', 'Ãº': 'ú', 'Ã½': 'ý',
        'Ä\u008d': 'č', 'Ä\u008f': 'ď', 'Ä\u009b': 'ě', 'Å\u0088': 'ň',
        'Å\u0099': 'ř', 'Å¡': 'š', 'Å¥': 'ť', 'Å¯': 'ů', 'Å¾': 'ž',

        // Základní diakritika - velká písmena
        'Ã\u0081': 'Á', 'Ã\u0089': 'É', 'Ã\u008d': 'Í', 'Ã\u0093': 'Ó', 'Ã\u009a': 'Ú', 'Ã\u009d': 'Ý',
        'Ä\u008c': 'Č', 'Ä\u008e': 'Ď', 'Ä\u009a': 'Ě', 'Å\u0087': 'Ň',
        'Å\u0098': 'Ř', 'Å ': 'Š', 'Å¤': 'Ť', 'Å®': 'Ů', 'Å½': 'Ž',

        // Alternativní kódování
        'Ä\u008d': 'č', 'Ä\u009b': 'ě', 'Å\u0088': 'ň', 'Å\u0099': 'ř', 'Å¡': 'š', 'Å¯': 'ů', 'Å¾': 'ž',
        'Ä\u008c': 'Č', 'Ä\u009a': 'Ě', 'Å\u0087': 'Ň', 'Å\u0098': 'Ř', 'Å ': 'Š', 'Å®': 'Ů', 'Å½': 'Ž',

        // Specifické problematické kombinace
        'Ã¨': 'è', 'Ã¬': 'ì', 'Ã²': 'ò', 'Ã¹': 'ù', 'Ã ': 'à',
        'Ã¤': 'ä', 'Ã¶': 'ö', 'Ã¼': 'ü', 'Ã§': 'ç',

        // Opravy pro časté chyby
        'Nìmcové': 'Němcové', 'Nímcové': 'Němcové',
        'Èe': 'če', 'Øe': 'ře', 'Še': 'še', 'Že': 'že',
        'Èi': 'či', 'Øi': 'ři', 'Ši': 'ši', 'Ži': 'ži',
        'È': 'č', 'Ø': 'ř', 'Š': 'š', 'Ž': 'ž', 'Ò': 'ň', 'Ù': 'ů'
    };

    let result = text;

    // Aplikujeme opravy v pořadí od nejspecifičtějších po nejobecnější
    for (const [wrong, correct] of Object.entries(replacements)) {
        if (result.includes(wrong)) {
            result = result.replace(new RegExp(wrong.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), correct);
        }
    }

    return result;
}

console.log('📊 Extraktor adres z Excel souboru');
console.log('=====================================\n');

try {
    // Načtení Excel souboru
    console.log('📂 Načítám soubor souradnice.xls...');
    const workbook = XLSX.readFile('souradnice.xls');
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    console.log(`📋 Název listu: ${sheetName}`);

    // Převod na JSON
    const data = XLSX.utils.sheet_to_json(worksheet);
    console.log(`📊 Celkem řádků: ${data.length}\n`);

    // Zobrazení prvních pár řádků pro analýzu struktury
    console.log('🔍 Struktura dat (první 3 řádky):');
    console.log('=====================================');
    data.slice(0, 3).forEach((row, index) => {
        console.log(`\nŘádek ${index + 1}:`);
        Object.keys(row).forEach(key => {
            if (row[key] && typeof row[key] === 'string' && row[key].length > 0) {
                console.log(`  ${key}: ${row[key]}`);
            }
        });
    });

    console.log('\n📍 Extrakce adres...');
    console.log('=====================================');

    // Extrakce adres
    const addresses = [];
    const uniqueAddresses = new Set();

    data.forEach((row, index) => {
        // Hledáme sloupce s adresními údaji a opravujeme kódování
        const street = fixCzechEncoding(row['Recipient Street'] || row['Ulice'] || row['Street'] || '');
        const city = fixCzechEncoding(row['Recipient City'] || row['Město'] || row['City'] || '');
        const postCode = fixCzechEncoding(row['Recipient Post Code'] || row['PSČ'] || row['PostCode'] || '');
        const name = fixCzechEncoding(row['Recipient Name'] || row['Recipient Company Name'] || row['Jméno'] || row['Name'] || `Místo ${index + 1}`);

        // Sestavení adresy
        let fullAddress = '';
        if (street && street.toString().trim()) {
            fullAddress += street.toString().trim();
        }
        if (city && city.toString().trim()) {
            if (fullAddress) fullAddress += ', ';
            fullAddress += city.toString().trim();
        }
        if (postCode && postCode.toString().trim()) {
            if (fullAddress) fullAddress += ', ';
            fullAddress += postCode.toString().trim();
        }

        // Opravíme kódování celé adresy
        fullAddress = fixCzechEncoding(fullAddress);

        // Přidáme pouze platné a unikátní adresy
        if (fullAddress.length > 10 && !uniqueAddresses.has(fullAddress)) {
            uniqueAddresses.add(fullAddress);
            addresses.push({
                id: addresses.length + 1,
                name: fixCzechEncoding(name.toString().trim()),
                address: fullAddress,
                street: fixCzechEncoding(street.toString().trim()),
                city: fixCzechEncoding(city.toString().trim()),
                postCode: fixCzechEncoding(postCode.toString().trim())
            });
        }
    });

    console.log(`✅ Nalezeno ${addresses.length} unikátních adres\n`);

    // Zobrazení všech adres
    console.log('📋 Seznam všech adres:');
    console.log('=====================================');
    addresses.forEach((addr, index) => {
        console.log(`${index + 1}. ${addr.name}`);
        console.log(`   📍 ${addr.address}`);
        if (index < addresses.length - 1) console.log('');
    });

    // Uložení do JSON souboru pro další použití
    const outputData = {
        totalAddresses: addresses.length,
        extractedAt: new Date().toISOString(),
        addresses: addresses
    };

    // Uložení s UTF-8 kódováním pro správné zobrazení češtiny
    fs.writeFileSync('extracted-addresses.json', JSON.stringify(outputData, null, 2), { encoding: 'utf8' });
    console.log('\n💾 Adresy uloženy do souboru: extracted-addresses.json');

    // Vytvoření jednoduchého seznamu pro copy-paste s UTF-8 kódováním
    const simpleList = addresses.map(addr => addr.address).join('\n');
    fs.writeFileSync('addresses-list.txt', simpleList, { encoding: 'utf8' });
    console.log('📝 Jednoduchý seznam uložen do: addresses-list.txt');

    // Vytvoření seznamu s názvy pro lepší orientaci
    const detailedList = addresses.map((addr, index) =>
        `${index + 1}. ${addr.name}\n   📍 ${addr.address}`
    ).join('\n\n');
    fs.writeFileSync('addresses-detailed.txt', detailedList, { encoding: 'utf8' });
    console.log('📋 Detailní seznam uložen do: addresses-detailed.txt');

    console.log('\n🎯 HOTOVO!');
    console.log('=====================================');
    console.log('Nyní můžete:');
    console.log('1. Zkontrolovat soubor extracted-addresses.json');
    console.log('2. Spustit: node route-planner.js pro automatické plánování');
    console.log('3. Nebo ručně zkopírovat adresy z addresses-list.txt');

} catch (error) {
    console.error('❌ Chyba při zpracování souboru:', error.message);
    console.log('\n💡 Možné příčiny:');
    console.log('- Soubor souřadnice.xls neexistuje');
    console.log('- Soubor je poškozen nebo má nesprávný formát');
    console.log('- Nemáte oprávnění ke čtení souboru');
}
