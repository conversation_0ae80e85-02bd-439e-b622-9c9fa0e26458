const XLSX = require('xlsx');
const fs = require('fs');
const iconv = require('iconv-lite');

// Nastavení kódování pro správné zobrazení češtiny
process.stdout.setEncoding('utf8');

console.log('📊 Extraktor adres z Excel souboru (UTF-8 optimalizovaný)');
console.log('=========================================================\n');

try {
    // Načtení Excel souboru s explicitním kódováním
    console.log('📂 Načítám soubor souradnice.xls...');
    
    // Načteme soubor jako buffer a zkusíme různá kódování
    const fileBuffer = fs.readFileSync('souradnice.xls');
    
    // Zkusíme načíst s různými možnostmi kódování
    const workbook = XLSX.read(fileBuffer, { 
        type: 'buffer',
        codepage: 1250, // Windows-1250 pro češtinu
        cellText: true,
        cellDates: true
    });
    
    // Získáme první list
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    console.log(`📋 Název listu: ${sheetName}`);
    
    // Převedeme na JSON s explicitním nastavením
    const data = XLSX.utils.sheet_to_json(worksheet, {
        raw: false, // Převede všechno na text
        defval: '', // Výchozí hodnota pro prázdné buňky
        blankrows: false // Přeskočí prázdné řádky
    });
    
    console.log(`📊 Celkem řádků: ${data.length}`);
    
    // Zobrazíme strukturu dat (první 3 řádky)
    console.log('\n🔍 Struktura dat (první 3 řádky):');
    console.log('=====================================\n');
    
    data.slice(0, 3).forEach((row, index) => {
        console.log(`Řádek ${index + 1}:`);
        Object.keys(row).forEach(key => {
            const value = row[key];
            if (value && value.toString().trim()) {
                console.log(`  ${key}: ${value}`);
            }
        });
        console.log('');
    });
    
    console.log('📍 Extrakce adres...');
    console.log('=====================================');
    
    // Extrakce adres
    const addresses = [];
    const uniqueAddresses = new Set();
    
    data.forEach((row, index) => {
        // Hledáme sloupce s adresními údaji - bez dalších úprav kódování
        const street = row['Recipient Street'] || row['Ulice'] || row['Street'] || '';
        const city = row['Recipient City'] || row['Město'] || row['City'] || '';
        const postCode = row['Recipient Post Code'] || row['PSČ'] || row['PostCode'] || '';
        const name = row['Recipient Name'] || row['Recipient Company Name'] || row['Jméno'] || row['Name'] || `Místo ${index + 1}`;
        
        // Sestavení adresy
        let fullAddress = '';
        if (street && street.toString().trim()) {
            fullAddress += street.toString().trim();
        }
        if (city && city.toString().trim()) {
            if (fullAddress) fullAddress += ', ';
            fullAddress += city.toString().trim();
        }
        if (postCode && postCode.toString().trim()) {
            if (fullAddress) fullAddress += ', ';
            fullAddress += postCode.toString().trim();
        }
        
        // Přidáme pouze platné a unikátní adresy
        if (fullAddress.length > 10 && !uniqueAddresses.has(fullAddress)) {
            uniqueAddresses.add(fullAddress);
            addresses.push({
                id: addresses.length + 1,
                name: name.toString().trim(),
                address: fullAddress,
                street: street.toString().trim(),
                city: city.toString().trim(),
                postCode: postCode.toString().trim()
            });
        }
    });
    
    console.log(`✅ Nalezeno ${addresses.length} unikátních adres\n`);
    
    // Zobrazení prvních 10 adres pro kontrolu
    console.log('📋 Prvních 10 adres (kontrola kódování):');
    console.log('=====================================');
    addresses.slice(0, 10).forEach((addr, index) => {
        console.log(`${index + 1}. ${addr.name}`);
        console.log(`   📍 ${addr.address}`);
        if (index < addresses.length - 1) console.log('');
    });
    
    // Uložení do JSON souboru pro další použití
    const outputData = {
        totalAddresses: addresses.length,
        extractedAt: new Date().toISOString(),
        encoding: 'UTF-8',
        sourceEncoding: 'Windows-1250',
        addresses: addresses
    };
    
    // Uložení s UTF-8 kódováním
    fs.writeFileSync('extracted-addresses-utf8.json', JSON.stringify(outputData, null, 2), { encoding: 'utf8' });
    console.log('\n💾 Adresy uloženy do souboru: extracted-addresses-utf8.json');
    
    // Vytvoření jednoduchého seznamu pro copy-paste s UTF-8 kódováním
    const simpleList = addresses.map(addr => addr.address).join('\n');
    fs.writeFileSync('addresses-list-utf8.txt', simpleList, { encoding: 'utf8' });
    console.log('📝 Jednoduchý seznam uložen do: addresses-list-utf8.txt');
    
    // Vytvoření seznamu s názvy pro lepší orientaci
    const detailedList = addresses.map((addr, index) => 
        `${index + 1}. ${addr.name}\n   📍 ${addr.address}`
    ).join('\n\n');
    fs.writeFileSync('addresses-detailed-utf8.txt', detailedList, { encoding: 'utf8' });
    console.log('📋 Detailní seznam uložen do: addresses-detailed-utf8.txt');
    
    // Kontrola specifických problematických znaků
    console.log('\n🔍 Kontrola kódování:');
    console.log('=====================================');
    
    const problematicChars = ['Nímcové', 'Nìmcové', 'Èe', 'Øe', 'Še', 'Že'];
    let foundProblems = false;
    
    addresses.forEach(addr => {
        problematicChars.forEach(char => {
            if (addr.address.includes(char) || addr.name.includes(char)) {
                console.log(`⚠️ Nalezen problematický znak "${char}" v: ${addr.name} - ${addr.address}`);
                foundProblems = true;
            }
        });
    });
    
    if (!foundProblems) {
        console.log('✅ Žádné problematické znaky nenalezeny!');
    }
    
    // Kontrola správných českých znaků
    const czechChars = ['ě', 'š', 'č', 'ř', 'ž', 'ý', 'á', 'í', 'é', 'ú', 'ů', 'ť', 'ď', 'ň'];
    let czechFound = 0;
    
    addresses.forEach(addr => {
        czechChars.forEach(char => {
            if (addr.address.toLowerCase().includes(char) || addr.name.toLowerCase().includes(char)) {
                czechFound++;
            }
        });
    });
    
    console.log(`✅ Nalezeno ${czechFound} výskytů správných českých znaků`);
    
    console.log('\n🎯 HOTOVO!');
    console.log('=====================================');
    console.log('Vytvořené soubory s UTF-8 kódováním:');
    console.log('• extracted-addresses-utf8.json - všechny adresy');
    console.log('• addresses-list-utf8.txt - jednoduchý seznam');
    console.log('• addresses-detailed-utf8.txt - detailní seznam');
    console.log('\nNyní můžete:');
    console.log('1. Zkontrolovat kódování v souborech');
    console.log('2. Spustit: node group-addresses.js pro seskupení');
    console.log('3. Spustit: node route-planner.js pro automatické plánování');
    
} catch (error) {
    console.error('❌ Chyba při zpracování:', error.message);
    console.log('\n💡 Možná řešení:');
    console.log('1. Zkontrolujte, že soubor souradnice.xls existuje');
    console.log('2. Zkuste uložit Excel soubor jako CSV s UTF-8 kódováním');
    console.log('3. Nebo použijte původní extract-addresses.js');
    
    if (error.message.includes('iconv-lite')) {
        console.log('\n📦 Instalace chybějící závislosti:');
        console.log('npm install iconv-lite');
    }
}
