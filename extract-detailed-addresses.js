const XLSX = require('xlsx');
const fs = require('fs');

// Nastavení kódování pro správné zobrazení češtiny
process.stdout.setEncoding('utf8');

console.log('📊 Extraktor detailních adres pro Google Maps');
console.log('==============================================\n');

// Funkce pro opravu konkrétních chyb v kódování
function fixSpecificEncodingErrors(text) {
    if (!text || typeof text !== 'string') return text;
    
    const corrections = {
        'Chelèice': 'Chelčice',
        'Libìjovice': 'Libějovice',
        'Nìmcové': 'Němcové',
        'Nìmcová': 'Němcová',
        'Jiøí': 'Jiř<PERSON>',
        'Køivanec': 'Křivanec',
        'Chmelenskeho': 'Ch<PERSON>enského',
        'Chelèickeho': 'Chelčického',
        'ì': 'ě',
        'ø': 'ř',
        'è': 'č',
        'ò': 'ň',
        'ù': 'ů',
        'Ì': 'Ě',
        'Ø': 'Ř',
        'È': 'Č',
        'Ò': 'Ň',
        'Ù': 'Ů'
    };
    
    let result = text;
    for (const [wrong, correct] of Object.entries(corrections)) {
        if (result.includes(wrong)) {
            result = result.replace(new RegExp(wrong.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), correct);
        }
    }
    
    return result;
}

try {
    // Načtení Excel souboru
    console.log('📂 Načítám soubor souradnice.xls...');
    
    const fileBuffer = fs.readFileSync('souradnice.xls');
    const workbook = XLSX.read(fileBuffer, { 
        type: 'buffer',
        cellText: false,
        cellDates: true,
        raw: true
    });
    
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    console.log(`📋 Název listu: ${sheetName}`);
    
    const data = XLSX.utils.sheet_to_json(worksheet, {
        raw: false,
        defval: '',
        blankrows: false
    });
    
    console.log(`📊 Celkem řádků: ${data.length}`);
    
    console.log('📍 Extrakce detailních adres...');
    console.log('=====================================');
    
    const addresses = [];
    const uniqueAddresses = new Set();
    
    data.forEach((row, index) => {
        // Extrakce všech dostupných informací
        const companyName = fixSpecificEncodingErrors(row['Recipient Company Name'] || '');
        const recipientName = fixSpecificEncodingErrors(row['Recipient Name'] || '');
        const surname = fixSpecificEncodingErrors(row['Recipient Surname'] || '');
        const street = fixSpecificEncodingErrors(row['Recipient Street'] || '');
        const city = fixSpecificEncodingErrors(row['Recipient City'] || '');
        const postCode = fixSpecificEncodingErrors(row['Recipient Post Code'] || '');
        const phone = row['Recipient Phone GSM'] || '';
        
        // Sestavení adresy
        let fullAddress = '';
        if (street && street.trim()) {
            fullAddress += street.trim();
        }
        if (city && city.trim()) {
            if (fullAddress) fullAddress += ', ';
            fullAddress += city.trim();
        }
        if (postCode && postCode.trim()) {
            if (fullAddress) fullAddress += ', ';
            fullAddress += postCode.trim();
        }
        
        // Sestavení názvu/jména pro zobrazení
        let displayName = '';
        let personName = '';
        
        // Priorita: Company Name > Recipient Name > Surname
        if (companyName && companyName.trim()) {
            displayName = companyName.trim();
        } else if (recipientName && recipientName.trim()) {
            displayName = recipientName.trim();
        } else if (surname && surname.trim()) {
            displayName = surname.trim();
        } else {
            displayName = `Místo ${index + 1}`;
        }
        
        // Sestavení jména osoby (pro dodatečné info)
        if (surname && surname.trim() && surname !== displayName) {
            personName = surname.trim();
        } else if (recipientName && recipientName.trim() && recipientName !== displayName) {
            personName = recipientName.trim();
        }
        
        // Vytvoření kompletního popisu pro Google Maps
        let googleMapsLabel = displayName;
        if (personName && personName !== displayName) {
            googleMapsLabel = `${displayName} - ${personName}`;
        }
        
        // Přidáme pouze platné a unikátní adresy
        if (fullAddress.length > 10 && !uniqueAddresses.has(fullAddress)) {
            uniqueAddresses.add(fullAddress);
            addresses.push({
                id: addresses.length + 1,
                displayName: displayName,
                personName: personName,
                googleMapsLabel: googleMapsLabel,
                companyName: companyName,
                recipientName: recipientName,
                surname: surname,
                address: fullAddress,
                street: street,
                city: city,
                postCode: postCode,
                phone: phone,
                fullGoogleMapsText: `${googleMapsLabel}, ${fullAddress}`,
                originalData: row
            });
        }
    });
    
    console.log(`✅ Nalezeno ${addresses.length} unikátních adres\n`);
    
    // Zobrazení prvních 10 adres ve formátu pro Google Maps
    console.log('📋 Prvních 10 adres (formát pro Google Maps):');
    console.log('=====================================');
    addresses.slice(0, 10).forEach((addr, index) => {
        console.log(`${index + 1}. ${addr.googleMapsLabel}`);
        console.log(`   📍 ${addr.address}`);
        console.log(`   🗺️ Pro Google Maps: "${addr.fullGoogleMapsText}"`);
        if (addr.phone) {
            console.log(`   📞 ${addr.phone}`);
        }
        console.log('');
    });
    
    // Uložení do JSON souboru
    const outputData = {
        totalAddresses: addresses.length,
        extractedAt: new Date().toISOString(),
        encoding: 'Opraveno pro Google Maps',
        format: 'Název/Firma - Jméno osoby, Adresa',
        addresses: addresses
    };
    
    fs.writeFileSync('extracted-addresses-google-maps.json', JSON.stringify(outputData, null, 2), { encoding: 'utf8' });
    console.log('💾 Detailní adresy uloženy do: extracted-addresses-google-maps.json');
    
    // Vytvoření seznamu pro Google Maps
    const googleMapsList = addresses.map(addr => addr.fullGoogleMapsText).join('\n');
    fs.writeFileSync('addresses-google-maps.txt', googleMapsList, { encoding: 'utf8' });
    console.log('📝 Seznam pro Google Maps uložen do: addresses-google-maps.txt');
    
    // Vytvoření detailního seznamu
    const detailedList = addresses.map((addr, index) => {
        let details = `${index + 1}. ${addr.googleMapsLabel}\n   📍 ${addr.address}`;
        if (addr.phone) {
            details += `\n   📞 ${addr.phone}`;
        }
        if (addr.companyName && addr.personName) {
            details += `\n   🏢 ${addr.companyName}`;
            details += `\n   👤 ${addr.personName}`;
        }
        return details;
    }).join('\n\n');
    
    fs.writeFileSync('addresses-detailed-google-maps.txt', detailedList, { encoding: 'utf8' });
    console.log('📋 Detailní seznam uložen do: addresses-detailed-google-maps.txt');
    
    // Vytvoření ukázkových tras pro Google Maps (po 8 adresách)
    console.log('\n🗺️ Vytváření ukázkových tras pro Google Maps...');
    const routeSize = 8;
    const routes = [];
    
    for (let i = 0; i < Math.min(addresses.length, 24); i += routeSize) {
        const routeAddresses = addresses.slice(i, i + routeSize);
        const routeNumber = Math.floor(i / routeSize) + 1;
        
        const routeData = {
            id: routeNumber,
            name: `Google Maps Trasa ${routeNumber}`,
            addressCount: routeAddresses.length,
            addresses: routeAddresses,
            googleMapsTexts: routeAddresses.map(addr => addr.fullGoogleMapsText)
        };
        
        routes.push(routeData);
        
        // Vytvoření souboru pro každou trasu
        const routeContent = [
            `🗺️ GOOGLE MAPS TRASA ${routeNumber}`,
            `Počet zastávek: ${routeAddresses.length}`,
            `Vygenerováno: ${new Date().toLocaleString('cs-CZ')}`,
            '='.repeat(60),
            '',
            'SEZNAM PRO GOOGLE MAPS:',
            '(zkopírujte každý řádek jako samostatný bod)',
            '',
            ...routeAddresses.map(addr => addr.fullGoogleMapsText),
            '',
            '='.repeat(60),
            'DETAILNÍ INFORMACE:',
            '',
            ...routeAddresses.map((addr, index) => {
                let info = `${index + 1}. ${addr.googleMapsLabel}\n   📍 ${addr.address}`;
                if (addr.phone) {
                    info += `\n   📞 ${addr.phone}`;
                }
                return info;
            }),
            '',
            '='.repeat(60),
            `Konec trasy ${routeNumber}`
        ].join('\n');
        
        fs.writeFileSync(`google-maps-trasa-${routeNumber}.txt`, routeContent, { encoding: 'utf8' });
    }
    
    console.log(`📁 Vytvořeno ${routes.length} ukázkových tras pro Google Maps`);
    
    // Uložení všech tras
    fs.writeFileSync('google-maps-routes.json', JSON.stringify({ routes }, null, 2), { encoding: 'utf8' });
    
    console.log('\n🎯 HOTOVO!');
    console.log('=====================================');
    console.log('Vytvořené soubory pro Google Maps:');
    console.log('• extracted-addresses-google-maps.json - všechny detailní adresy');
    console.log('• addresses-google-maps.txt - seznam pro copy-paste');
    console.log('• addresses-detailed-google-maps.txt - detailní přehled');
    console.log(`• google-maps-trasa-1.txt až google-maps-trasa-${routes.length}.txt - ukázkové trasy`);
    console.log('• google-maps-routes.json - všechny trasy');
    
    console.log('\nFormát pro Google Maps:');
    console.log('• "OX BOX Chelčice - Olga Nová, Chelčice 5, 389 01, Chelčice"');
    console.log('• "Barbora Benešová, Jinín 55, 386 01, Jinín"');
    console.log('• "Pazdera Jiří - Jiří Pazdera, Libějovice 84, 387 72, Libějovice"');
    
    console.log('\nNyní můžete:');
    console.log('1. Spustit: node google-maps-planner.js pro automatické přidání');
    console.log('2. Ručně zkopírovat adresy z google-maps-trasa-X.txt souborů');
    console.log('3. Každý řádek vložit jako samostatný bod do Google Maps');
    
} catch (error) {
    console.error('❌ Chyba při zpracování:', error.message);
    console.log('\n💡 Zkontrolujte, že soubor souradnice.xls existuje');
}
