const fs = require('fs');

// Nastavení kódování pro správné zobrazení češtiny
process.stdout.setEncoding('utf8');

console.log('🏘️ Seskupování adres podle měst');
console.log('=====================================\n');

try {
    // Načtení extrahovaných adres s UTF-8 kódováním
    let data, addresses;

    // Zkusíme nejdříve UTF-8 verzi, pak původní
    try {
        data = JSON.parse(fs.readFileSync('extracted-addresses-utf8.json', 'utf8'));
        addresses = data.addresses;
        console.log('✅ Použita UTF-8 verze adres');
    } catch (e) {
        try {
            data = JSON.parse(fs.readFileSync('extracted-addresses.json', 'utf8'));
            addresses = data.addresses;
            console.log('⚠️ Použita původní verze adres');
        } catch (e2) {
            throw new Error('Nenalezen žádný soubor s adresami. Spusťte nejdříve extract-addresses-utf8.js');
        }
    }

    console.log(`📊 Celkem adres: ${addresses.length}`);

    // Seskupení podle měst
    const cityGroups = {};

    addresses.forEach(addr => {
        const city = addr.city.toLowerCase().trim();
        if (!cityGroups[city]) {
            cityGroups[city] = [];
        }
        cityGroups[city].push(addr);
    });

    // Seřazení měst podle počtu adres (sestupně)
    const sortedCities = Object.keys(cityGroups).sort((a, b) =>
        cityGroups[b].length - cityGroups[a].length
    );

    console.log(`🏙️ Nalezeno ${sortedCities.length} různých měst/obcí\n`);

    // Zobrazení statistik
    console.log('📈 Statistiky podle měst:');
    console.log('=====================================');
    sortedCities.forEach((city, index) => {
        const count = cityGroups[city].length;
        const percentage = ((count / addresses.length) * 100).toFixed(1);
        console.log(`${index + 1}. ${city.charAt(0).toUpperCase() + city.slice(1)}: ${count} adres (${percentage}%)`);
    });

    console.log('\n🗺️ Vytváření souborů pro jednotlivá města...');
    console.log('=====================================');

    // Vytvoření souborů pro každé město s více než 5 adresami
    const minAddressesForFile = 3;
    let filesCreated = 0;

    sortedCities.forEach(city => {
        const addresses = cityGroups[city];
        if (addresses.length >= minAddressesForFile) {
            const cityName = city.charAt(0).toUpperCase() + city.slice(1);
            const fileName = `adresy-${city.replace(/\s+/g, '-').toLowerCase()}.txt`;

            // Vytvoření obsahu souboru
            const content = [
                `📍 ADRESY - ${cityName.toUpperCase()}`,
                `Celkem adres: ${addresses.length}`,
                `Vygenerováno: ${new Date().toLocaleString('cs-CZ')}`,
                '='.repeat(50),
                '',
                ...addresses.map((addr, index) =>
                    `${index + 1}. ${addr.name}\n   📍 ${addr.address}`
                ),
                '',
                '='.repeat(50),
                `Konec seznamu - ${cityName}`
            ].join('\n');

            fs.writeFileSync(fileName, content, { encoding: 'utf8' });
            console.log(`✅ ${fileName} (${addresses.length} adres)`);
            filesCreated++;
        }
    });

    console.log(`\n📁 Vytvořeno ${filesCreated} souborů pro města s ${minAddressesForFile}+ adresami`);

    // Vytvoření souhrnného souboru s optimálním rozdělením tras
    console.log('\n🚗 Návrh optimálního rozdělení tras...');
    console.log('=====================================');

    const routeGroups = [];
    const maxAddressesPerRoute = 8; // Limit pro mapy.cz

    // Hlavní města s nejvíce adresami
    const mainCities = sortedCities.filter(city => cityGroups[city].length >= 5);

    mainCities.forEach(city => {
        const cityAddresses = cityGroups[city];
        const cityName = city.charAt(0).toUpperCase() + city.slice(1);

        // Rozdělení na menší skupiny pokud je příliš mnoho adres
        if (cityAddresses.length > maxAddressesPerRoute) {
            const chunks = [];
            for (let i = 0; i < cityAddresses.length; i += maxAddressesPerRoute) {
                chunks.push(cityAddresses.slice(i, i + maxAddressesPerRoute));
            }

            chunks.forEach((chunk, index) => {
                routeGroups.push({
                    name: `${cityName} - část ${index + 1}`,
                    addresses: chunk,
                    city: cityName
                });
            });
        } else {
            routeGroups.push({
                name: cityName,
                addresses: cityAddresses,
                city: cityName
            });
        }
    });

    // Menší města spojíme do kombinovaných tras
    const smallCities = sortedCities.filter(city =>
        cityGroups[city].length < 5 && cityGroups[city].length >= 2
    );

    if (smallCities.length > 0) {
        let combinedAddresses = [];
        let combinedNames = [];

        smallCities.forEach(city => {
            const cityAddresses = cityGroups[city];
            const cityName = city.charAt(0).toUpperCase() + city.slice(1);

            if (combinedAddresses.length + cityAddresses.length <= maxAddressesPerRoute) {
                combinedAddresses.push(...cityAddresses);
                combinedNames.push(cityName);
            } else {
                // Uložíme aktuální kombinovanou trasu
                if (combinedAddresses.length > 0) {
                    routeGroups.push({
                        name: `Kombinovaná trasa: ${combinedNames.join(', ')}`,
                        addresses: combinedAddresses,
                        city: 'Kombinovaná'
                    });
                }

                // Začneme novou kombinovanou trasu
                combinedAddresses = [...cityAddresses];
                combinedNames = [cityName];
            }
        });

        // Přidáme poslední kombinovanou trasu
        if (combinedAddresses.length > 0) {
            routeGroups.push({
                name: `Kombinovaná trasa: ${combinedNames.join(', ')}`,
                addresses: combinedAddresses,
                city: 'Kombinovaná'
            });
        }
    }

    // Zobrazení návrhů tras
    console.log(`\n📋 Návrh ${routeGroups.length} optimálních tras:`);
    console.log('=====================================');

    routeGroups.forEach((group, index) => {
        console.log(`\n${index + 1}. ${group.name}`);
        console.log(`   📊 Počet adres: ${group.addresses.length}`);
        console.log(`   📍 Ukázka adres:`);
        group.addresses.slice(0, 3).forEach((addr, i) => {
            console.log(`      ${i + 1}. ${addr.name} - ${addr.address}`);
        });
        if (group.addresses.length > 3) {
            console.log(`      ... a dalších ${group.addresses.length - 3} adres`);
        }
    });

    // Uložení návrhů tras do souboru
    const routeSummary = {
        totalRoutes: routeGroups.length,
        totalAddresses: addresses.length,
        generatedAt: new Date().toISOString(),
        routes: routeGroups.map((group, index) => ({
            id: index + 1,
            name: group.name,
            city: group.city,
            addressCount: group.addresses.length,
            addresses: group.addresses
        }))
    };

    fs.writeFileSync('route-suggestions.json', JSON.stringify(routeSummary, null, 2), { encoding: 'utf8' });
    console.log('\n💾 Návrhy tras uloženy do: route-suggestions.json');

    // Vytvoření jednoduchých textových souborů pro každou navrženou trasu
    routeGroups.forEach((group, index) => {
        const fileName = `trasa-${index + 1}-${group.name.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}.txt`;
        const content = [
            `🚗 TRASA ${index + 1}: ${group.name.toUpperCase()}`,
            `Počet zastávek: ${group.addresses.length}`,
            `Vygenerováno: ${new Date().toLocaleString('cs-CZ')}`,
            '='.repeat(60),
            '',
            'SEZNAM ADRES PRO MAPY.CZ:',
            '(zkopírujte a vložte do plánovače tras)',
            '',
            ...group.addresses.map(addr => addr.address),
            '',
            '='.repeat(60),
            'DETAILNÍ SEZNAM:',
            '',
            ...group.addresses.map((addr, i) =>
                `${i + 1}. ${addr.name}\n   📍 ${addr.address}`
            ),
            '',
            '='.repeat(60),
            `Konec trasy ${index + 1}`
        ].join('\n');

        fs.writeFileSync(fileName, content, { encoding: 'utf8' });
    });

    console.log(`📁 Vytvořeno ${routeGroups.length} souborů s trasami`);

    console.log('\n🎯 HOTOVO!');
    console.log('=====================================');
    console.log('Vytvořené soubory:');
    console.log('• extracted-addresses.json - všechny adresy');
    console.log('• addresses-detailed.txt - detailní seznam');
    console.log('• route-suggestions.json - návrhy tras');
    console.log(`• ${filesCreated} souborů podle měst`);
    console.log(`• ${routeGroups.length} souborů s optimálními trasami`);
    console.log('\nMůžete nyní:');
    console.log('1. Použít soubory tras pro plánování na mapy.cz');
    console.log('2. Spustit: node route-planner.js pro automatické plánování');
    console.log('3. Ručně zkopírovat adresy z jednotlivých souborů tras');

} catch (error) {
    console.error('❌ Chyba při zpracování:', error.message);
    console.log('\n💡 Ujistěte se, že existuje soubor extracted-addresses.json');
    console.log('Spusťte nejdříve: node extract-addresses.js');
}
