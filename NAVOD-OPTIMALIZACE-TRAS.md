# 🗺️ Návod na optimalizaci tras na Mapy.cz

## 🎯 Proč optimalizovat trasy?

Bez optimalizace by <PERSON><PERSON><PERSON><PERSON> musel:
- ❌ Jezdit tam a zpět
- ❌ Projížd<PERSON>t stejnými místy vícekrát  
- ❌ Ujet zbytečně mnoho kilometrů
- ❌ Strávit více času na cestě

S optimalizací:
- ✅ Nejkratší možná cesta
- ✅ Logické pořadí zastávek
- ✅ Úspora času a paliva
- ✅ Efektivní rozvoz

## 🚀 Automatická optimalizace (skript)

Naše skripty se pokusí automaticky najít a aktivovat optimalizaci:

```bash
# Spustí automatickou optimalizaci
node route-planner.js
# nebo
node smart-route-planner.js
```

Skript hledá tlačítka s texty:
- "Optimalizovat"
- "Nejkratší"
- "Nejrychlejš<PERSON>"
- "Optimalizace"

## 🖱️ Ruční optimalizace

### Metoda 1: Tlačítko optimalizace
1. **Přidejte všechny adresy** do plánovače tras
2. **Hledejte tlačítko** s textem:
   - 🔍 "Optimalizovat trasu"
   - 🔍 "Nejkratší cesta"
   - 🔍 "Nejrychlejší cesta"
   - 🔍 "Optimalizace"
3. **Klikněte na tlačítko** a počkejte na přepočítání

### Metoda 2: Nastavení tras
1. **Otevřete nastavení** plánovače tras (ikona ozubeného kola ⚙️)
2. **Hledejte možnosti**:
   - "Optimalizovat pořadí zastávek"
   - "Nejkratší cesta"
   - "Automatická optimalizace"
3. **Aktivujte optimalizaci** a potvrďte

### Metoda 3: Přetahování zastávek
1. **Ručně přetáhněte** zastávky myší
2. **Změňte pořadí** tak, aby byla cesta logická
3. **Sledujte změny** celkové vzdálenosti a času

## 📍 Tipy pro optimální trasy

### 🏘️ Geografické seskupení
- Seskupte adresy podle **měst/oblastí**
- Použijte naše připravené soubory:
  ```
  adresy-strakonice.txt    (67 adres)
  adresy-vodňany.txt       (30 adres)
  adresy-radomyšl.txt      (6 adres)
  ```

### 🚗 Optimální trasy (max 8 adres)
Použijte naše připravené optimální trasy:
```
trasa-1-strakonice-----st-1.txt
trasa-2-strakonice-----st-2.txt
trasa-10-vod-any-----st-1.txt
...
```

### 🎯 Strategie plánování
1. **Začněte od výchozího bodu** (sklad, kancelář)
2. **Pokračujte po obvodu** oblasti
3. **Vracejte se spirálovitě** dovnitř
4. **Končete blízko výchozího bodu**

## 🔧 Řešení problémů

### ❌ Tlačítko optimalizace nenalezeno
**Řešení:**
1. Zkuste **obnovit stránku** (F5)
2. **Přidejte adresy znovu**
3. Hledejte v **menu nebo nastavení**
4. Použijte **ruční přetahování**

### ❌ Optimalizace nefunguje
**Řešení:**
1. **Zkontrolujte počet adres** (max 8-10 pro mapy.cz)
2. **Ověřte správnost adres**
3. **Zkuste menší skupiny** adres
4. **Použijte jiný prohlížeč**

### ❌ Trasa je stále nelogická
**Řešení:**
1. **Ručně přeuspořádejte** zastávky
2. **Rozdělte na menší trasy**
3. **Zkontrolujte dopravní omezení**
4. **Použijte alternativní mapy** (Google Maps, Waze)

## 📊 Kontrola optimalizace

### ✅ Dobře optimalizovaná trasa:
- Minimální počet křížení cest
- Logické geografické pořadí
- Krátká celková vzdálenost
- Rozumný celkový čas

### ❌ Špatně optimalizovaná trasa:
- Časté návráty do stejných oblastí
- Dlouhé přejezdy mezi vzdálenými body
- Zbytečné kilometry
- Nelogické pořadí zastávek

## 🎉 Výsledek

Po správné optimalizaci budete mít:
- **Nejkratší možnou cestu** mezi všemi zastávkami
- **Logické pořadí** návštěv
- **Úsporu času a paliva**
- **Efektivní rozvozový plán**

---

## 📞 Podpora

Pokud máte problémy s optimalizací:
1. Zkuste **různé metody** uvedené výše
2. **Rozdělte velké trasy** na menší části
3. Použijte **naše připravené optimální trasy**
4. V krajním případě **plánujte ručně** podle geografické logiky

**Tip:** Naše skripty už vytvořily optimálně rozdělené trasy - stačí je použít! 🚀
