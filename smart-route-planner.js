const { chromium } = require('playwright');

// Nastavení <PERSON> pro správné zobrazení češtiny
process.stdout.setEncoding('utf8');

(async () => {
    console.log('🧠 Chytrý plánovač tras - adaptivní přístup');
    console.log('==========================================\n');

    // Testovací adresy z první trasy
    const testAddresses = [
        { name: '<PERSON><PERSON><PERSON>', address: '46, Strakonice, 386 01' },
        { name: '<PERSON><PERSON><PERSON><PERSON>', address: 'Bahenní 1398, Strakonice, 386 01' },
        { name: '<PERSON>', address: 'Boženy Nímcové 1117, Strakonice, 386 01' },
        { name: '<PERSON><PERSON><PERSON>', address: 'Dražejov90, Strakonice, 386 01' },
        { name: 'CZ004CST', address: 'Dukelská 33, Strakonice, 386 01' }
    ];

    console.log(`📍 Testovací adresy (${testAddresses.length}):`);
    testAddresses.forEach((addr, i) => {
        console.log(`   ${i + 1}. ${addr.name} - ${addr.address}`);
    });

    console.log('\n🌐 Spouštím prohlížeč a otevírám mapy.cz...');

    // Spuštění prohlížeče
    const browser = await chromium.launch({
        headless: false,  // Zobrazíme prohlížeč
        slowMo: 300      // Zpomalíme pro lepší sledování
    });

    const context = await browser.newContext();
    const page = await context.newPage();

    try {
        // Otevření mapy.cz
        console.log('📍 Otevírám mapy.cz...');
        await page.goto('https://mapy.cz', { waitUntil: 'networkidle' });

        // Počkáme na načtení stránky
        await page.waitForTimeout(3000);

        // Zkusíme zavřít případné cookies dialog
        try {
            const cookiesButton = page.locator('button:has-text("Souhlasím")');
            if (await cookiesButton.count() > 0) {
                await cookiesButton.click();
                console.log('✅ Cookies dialog zavřen');
                await page.waitForTimeout(1000);
            }
        } catch (e) {
            console.log('ℹ️ Cookies dialog nenalezen nebo již zavřen');
        }

        // Klikneme na tlačítko pro plánování trasy
        console.log('🗺️ Aktivuji plánovač tras...');

        try {
            const routeButton = page.locator('button:has-text("Trasa")').first();
            if (await routeButton.count() > 0) {
                await routeButton.click();
                console.log('✅ Plánovač tras aktivován');
                await page.waitForTimeout(2000);
            }
        } catch (error) {
            console.log('⚠️ Problém s aktivací plánovače tras');
        }

        console.log('\n📍 Začínám chytré přidávání adres...');

        // Funkce pro nalezení dostupných input polí
        async function findAvailableInputs() {
            const inputSelectors = [
                'input[type="text"]',
                '[class*="route"] input',
                '.route-input input',
                'input[placeholder*="adres"]',
                'input[placeholder*="místo"]'
            ];

            for (const selector of inputSelectors) {
                const inputs = page.locator(selector);
                const count = await inputs.count();
                if (count > 0) {
                    console.log(`     🔍 Nalezeno ${count} input polí (${selector})`);
                    return { inputs, count, selector };
                }
            }
            return null;
        }

        // Funkce pro hledání tlačítka pro přidání dalšího bodu
        async function findAddButton() {
            const addButtonSelectors = [
                'button:has-text("+")',
                '.route-item-plus button',
                'button.plus',
                '[class*="plus"] button',
                '[class*="add"] button',
                'button[title*="přidat"]',
                'button[aria-label*="přidat"]',
                // Obecnější selektory
                'div[class*="route"] button:visible',
                'button:visible'
            ];

            for (const selector of addButtonSelectors) {
                try {
                    const buttons = page.locator(selector);
                    const count = await buttons.count();

                    if (count > 0) {
                        // Projdeme všechna tlačítka a hledáme to s "+"
                        for (let i = 0; i < count; i++) {
                            const button = buttons.nth(i);
                            if (await button.isVisible()) {
                                const text = await button.textContent();
                                const title = await button.getAttribute('title');
                                const ariaLabel = await button.getAttribute('aria-label');

                                if ((text && text.includes('+')) ||
                                    (title && title.toLowerCase().includes('přidat')) ||
                                    (ariaLabel && ariaLabel.toLowerCase().includes('přidat'))) {
                                    console.log(`     🎯 Nalezeno tlačítko pro přidání: "${text}" (${selector})`);
                                    return button;
                                }
                            }
                        }
                    }
                } catch (e) {
                    continue;
                }
            }
            return null;
        }

        // Přidání adres
        for (let i = 0; i < testAddresses.length; i++) {
            const addr = testAddresses[i];
            console.log(`\n   ${i + 1}/${testAddresses.length}: Přidávám ${addr.name}`);
            console.log(`   📍 Adresa: ${addr.address}`);

            try {
                // Najdeme dostupná input pole
                const inputInfo = await findAvailableInputs();

                if (!inputInfo) {
                    console.log(`     ❌ Žádná input pole nenalezena`);
                    continue;
                }

                let targetInput = null;

                // Pro první dvě adresy použijeme první dva inputy
                if (i < inputInfo.count) {
                    targetInput = inputInfo.inputs.nth(i);
                    console.log(`     🎯 Používám input ${i + 1}/${inputInfo.count}`);
                } else {
                    // Pro další adresy musíme najít tlačítko pro přidání
                    console.log(`     🔘 Potřebuji přidat nové input pole...`);

                    const addButton = await findAddButton();
                    if (addButton) {
                        await addButton.click();
                        console.log(`     ✅ Kliknuto na tlačítko pro přidání`);
                        await page.waitForTimeout(1500);

                        // Znovu najdeme input pole
                        const newInputInfo = await findAvailableInputs();
                        if (newInputInfo && newInputInfo.count > inputInfo.count) {
                            targetInput = newInputInfo.inputs.last();
                            console.log(`     🎯 Používám nově přidané input pole`);
                        } else {
                            console.log(`     ⚠️ Nové input pole se nepřidalo`);
                            targetInput = inputInfo.inputs.last();
                        }
                    } else {
                        console.log(`     ⚠️ Tlačítko pro přidání nenalezeno, používám poslední input`);
                        targetInput = inputInfo.inputs.last();
                    }
                }

                // Vyplníme adresu
                if (targetInput && await targetInput.isVisible()) {
                    console.log(`     ⌨️ Vyplňuji adresu...`);

                    // Vyčistíme pole a vyplníme novou adresu
                    await targetInput.click();
                    await page.waitForTimeout(300);
                    await targetInput.selectText();
                    await targetInput.fill(addr.address);
                    await page.waitForTimeout(1000);
                    await page.keyboard.press('Enter');
                    await page.waitForTimeout(2000);

                    console.log(`     ✅ Adresa "${addr.address}" přidána`);
                } else {
                    console.log(`     ❌ Input pole není dostupné`);
                }

            } catch (error) {
                console.log(`     ❌ Chyba při přidávání adresy: ${error.message}`);
            }

            // Pauza mezi adresami
            await page.waitForTimeout(1000);
        }

        console.log('\n🎯 Chytré přidávání dokončeno!');
        console.log('📊 Nyní optimalizuji pořadí zastávek...');

        // Počkáme chvíli, aby se trasa vykreslila
        await page.waitForTimeout(3000);

        // Funkce pro hledání a aktivaci optimalizace trasy
        async function optimizeRoute() {
            console.log('\n🔍 Hledám možnosti optimalizace trasy...');

            // Různé možné selektory pro optimalizaci
            const optimizeSelectors = [
                // Přímé texty
                'button:has-text("Optimalizovat")',
                'button:has-text("optimalizovat")',
                'button:has-text("Optimalizace")',
                'button:has-text("optimalizace")',
                'button:has-text("Nejkratší")',
                'button:has-text("nejkratší")',
                'button:has-text("Nejrychlejší")',
                'button:has-text("nejrychlejší")',

                // Atributy
                'button[title*="optimalizac"]',
                'button[title*="Optimalizac"]',
                'button[title*="nejkratší"]',
                'button[title*="nejrychlejší"]',
                'button[aria-label*="optimalizac"]',
                'button[data-tooltip*="optimalizac"]',

                // CSS třídy
                'button[class*="optimize"]',
                'button[class*="Optimize"]',
                '.optimize-button',
                '.route-optimize',
                '[class*="route-optimization"]',

                // Obecnější selektory v kontextu tras
                'div[class*="route"] button:has-text("Optimalizovat")',
                '.route-controls button',
                '.route-options button',
                '[class*="route-settings"] button'
            ];

            let optimizeButton = null;

            for (const selector of optimizeSelectors) {
                try {
                    console.log(`   🔍 Zkouším: ${selector}`);
                    const buttons = page.locator(selector);
                    const count = await buttons.count();

                    if (count > 0) {
                        console.log(`   📊 Nalezeno ${count} tlačítek`);

                        for (let i = 0; i < count; i++) {
                            const button = buttons.nth(i);
                            if (await button.isVisible()) {
                                const text = await button.textContent();
                                const title = await button.getAttribute('title');
                                console.log(`   🎯 Tlačítko ${i}: text="${text}", title="${title}"`);

                                if ((text && (text.toLowerCase().includes('optimalizac') ||
                                             text.toLowerCase().includes('nejkratší') ||
                                             text.toLowerCase().includes('nejrychlejší'))) ||
                                    (title && (title.toLowerCase().includes('optimalizac') ||
                                              title.toLowerCase().includes('nejkratší') ||
                                              title.toLowerCase().includes('nejrychlejší')))) {
                                    optimizeButton = button;
                                    console.log(`   ✅ Nalezeno optimalizační tlačítko!`);
                                    break;
                                }
                            }
                        }
                        if (optimizeButton) break;
                    }
                } catch (e) {
                    console.log(`   ⚠️ Chyba s ${selector}: ${e.message}`);
                    continue;
                }
            }

            return optimizeButton;
        }

        // Pokusíme se najít a aktivovat optimalizaci
        const optimizeButton = await optimizeRoute();

        if (optimizeButton) {
            try {
                console.log('\n🚀 Aktivuji optimalizaci trasy...');
                await optimizeButton.click();
                await page.waitForTimeout(2000);
                console.log('✅ Optimalizace trasy aktivována!');

                // Počkáme na přepočítání trasy
                console.log('⏳ Čekám na přepočítání optimální trasy...');
                await page.waitForTimeout(5000);
                console.log('✅ Trasa by měla být optimalizována!');

            } catch (error) {
                console.log(`❌ Chyba při aktivaci optimalizace: ${error.message}`);
            }
        } else {
            console.log('⚠️ Tlačítko optimalizace nenalezeno');
            console.log('💡 Zkuste ručně:');
            console.log('   1. Hledejte tlačítko "Optimalizovat" nebo "Nejkratší cesta"');
            console.log('   2. Nebo v nastavení tras aktivujte optimalizaci');
            console.log('   3. Případně přetáhněte zastávky myší pro změnu pořadí');
        }

        // Zkusíme také najít další možnosti optimalizace
        console.log('\n🔍 Hledám další možnosti optimalizace...');
        try {
            // Hledáme menu nebo nastavení tras
            const settingsSelectors = [
                'button[title*="nastavení"]',
                'button[title*="Nastavení"]',
                'button[title*="možnosti"]',
                'button[title*="Možnosti"]',
                '.route-settings',
                '.route-options',
                '[class*="settings"]',
                '[class*="options"]'
            ];

            for (const selector of settingsSelectors) {
                try {
                    const elements = page.locator(selector);
                    if (await elements.count() > 0) {
                        console.log(`📋 Nalezeno nastavení tras: ${selector}`);
                        // Můžeme zkusit kliknout a hledat optimalizaci uvnitř
                    }
                } catch (e) {
                    continue;
                }
            }

        } catch (e) {
            console.log('ℹ️ Další možnosti optimalizace nenalezeny');
        }

        console.log('\n⏳ Prohlížeč zůstane otevřený. Stiskněte Ctrl+C pro ukončení...');

        // Čekáme nekonečně dlouho, dokud uživatel neukončí
        await new Promise(() => {});

    } catch (error) {
        console.error('❌ Došlo k chybě:', error.message);
    } finally {
        console.log('🔚 Zavírám prohlížeč...');
        await browser.close();
    }
})();
