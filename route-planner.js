const { chromium } = require('playwright');
const XLSX = require('xlsx');
const fs = require('fs');

// Nastavení kódování pro správné zobrazení češtiny
process.stdout.setEncoding('utf8');

// Funkce pro opravu kódování českých znaků
function fixCzechEncoding(text) {
    if (!text || typeof text !== 'string') return text;

    const replacements = {
        'Ã¡': 'á', 'Ã©': 'é', 'Ã­': 'í', 'Ã³': 'ó', 'Ãº': 'ú', 'Å¯': 'ů',
        'Ã½': 'ý', 'Ä': 'č', 'Ä': 'ď', 'Ä': 'ě', 'Å': 'ň', 'Å': 'ř',
        'Å¡': 'š', 'Å¥': 'ť', 'Å¾': 'ž', 'Ã': 'Á', 'Ã': 'É', 'Ã': 'Í',
        'Ã': 'Ó', 'Ã': 'Ú', 'Å®': 'Ů', 'Ã': 'Ý', 'Ä': 'Č', 'Ä': 'Ď',
        'Ä': 'Ě', 'Å': 'Ň', 'Å': 'Ř', 'Å ': 'Š', 'Å¤': 'Ť', 'Å½': 'Ž',
        'Ã¨': 'è', 'Ã¬': 'ì', 'Ã²': 'ò', 'Ã¹': 'ù', 'Ã ': 'à',
        'Ã¤': 'ä', 'Ã¶': 'ö', 'Ã¼': 'ü', 'Ã': 'ß', 'Ã§': 'ç',
        'Èe': 'če', 'Øe': 'ře', 'Še': 'še', 'Že': 'že', 'Òa': 'ňa',
        'Èi': 'či', 'Øi': 'ři', 'Ši': 'ši', 'Ži': 'ži', 'Òi': 'ňi',
        'È': 'č', 'Ø': 'ř', 'Š': 'š', 'Ž': 'ž', 'Ò': 'ň', 'Ù': 'ů',
        'ì': 'í', 'è': 'č', 'ø': 'ř', 'š': 'š', 'ž': 'ž', 'ò': 'ň',
        'ù': 'ů', 'á': 'á', 'é': 'é', 'í': 'í', 'ó': 'ó', 'ú': 'ú',
        'ý': 'ý', 'ě': 'ě', 'ď': 'ď', 'ť': 'ť'
    };

    let result = text;
    for (const [wrong, correct] of Object.entries(replacements)) {
        result = result.replace(new RegExp(wrong, 'g'), correct);
    }

    return result;
}

(async () => {
    console.log('🚀 Spouštím plánovač tras z Excel souboru...');

    // Načtení dat z Excel souboru
    console.log('📊 Načítám data ze souboru souradnice.xls...');

    let addresses = [];
    try {
        const workbook = XLSX.readFile('souradnice.xls');
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(worksheet);

        console.log(`📋 Nalezeno ${data.length} záznamů v Excel souboru`);

        // Extrakce adres z dat s opravou kódování
        addresses = data.map((row, index) => {
            // Pokusíme se najít sloupce s adresou a opravíme kódování
            const street = fixCzechEncoding(row['Recipient Street'] || row['Ulice'] || row['Street'] || '');
            const city = fixCzechEncoding(row['Recipient City'] || row['Město'] || row['City'] || '');
            const postCode = fixCzechEncoding(row['Recipient Post Code'] || row['PSČ'] || row['PostCode'] || '');
            const name = fixCzechEncoding(row['Recipient Name'] || row['Recipient Company Name'] || row['Jméno'] || row['Name'] || `Adresa ${index + 1}`);

            // Sestavení adresy
            let fullAddress = '';
            if (street) fullAddress += street;
            if (city) {
                if (fullAddress) fullAddress += ', ';
                fullAddress += city;
            }
            if (postCode) {
                if (fullAddress) fullAddress += ', ';
                fullAddress += postCode;
            }

            // Opravíme kódování celé adresy
            fullAddress = fixCzechEncoding(fullAddress);

            return {
                name: fixCzechEncoding(name),
                address: fullAddress || 'Neznámá adresa',
                originalData: row
            };
        }).filter(addr => addr.address !== 'Neznámá adresa' && addr.address.length > 5);

        console.log(`✅ Zpracováno ${addresses.length} platných adres`);

        // Zobrazení prvních 5 adres pro kontrolu
        console.log('\n📍 Ukázka načtených adres:');
        addresses.slice(0, 5).forEach((addr, i) => {
            console.log(`   ${i + 1}. ${addr.name}: ${addr.address}`);
        });

        if (addresses.length > 5) {
            console.log(`   ... a dalších ${addresses.length - 5} adres`);
        }

    } catch (error) {
        console.error('❌ Chyba při načítání Excel souboru:', error.message);
        console.log('💡 Zkusím použít ukázkové adresy...');

        // Fallback - ukázkové adresy
        addresses = [
            { name: 'Strakonice - centrum', address: 'Velké náměstí 1, Strakonice, 386 01' },
            { name: 'Katovice', address: 'Katovice, 387 11' },
            { name: 'Vodňany', address: 'Vodňany, 389 01' },
            { name: 'Volenice', address: 'Volenice, 387 16' },
            { name: 'Radomy�l', address: 'Radomy�l, 387 31' }
        ];
    }

    if (addresses.length === 0) {
        console.error('❌ Žádné adresy k zpracování!');
        return;
    }

    // Omezení počtu adres pro demonstraci (mapy.cz mají limit)
    const maxAddresses = 10;
    if (addresses.length > maxAddresses) {
        console.log(`⚠️ Omezujem na prvních ${maxAddresses} adres (limit mapy.cz)`);
        addresses = addresses.slice(0, maxAddresses);
    }

    console.log('\n🌐 Spouštím prohlížeč a otevírám mapy.cz...');

    // Spuštění prohlížeče
    const browser = await chromium.launch({
        headless: false,  // Zobrazíme prohlížeč, aby uživatel viděl výsledek
        slowMo: 1000     // Zpomalíme pro lepší sledování
    });

    const context = await browser.newContext();
    const page = await context.newPage();

    try {
        // Otevření mapy.cz
        console.log('📍 Otevírám mapy.cz...');
        await page.goto('https://mapy.cz', { waitUntil: 'networkidle' });

        // Počkáme na načtení stránky
        await page.waitForTimeout(3000);

        // Zkusíme zavřít případné cookies dialog
        try {
            const cookiesButton = page.locator('button:has-text("Souhlasím")');
            if (await cookiesButton.count() > 0) {
                await cookiesButton.click();
                console.log('✅ Cookies dialog zavřen');
            }
        } catch (e) {
            console.log('ℹ️ Cookies dialog nenalezen nebo již zavřen');
        }

        await page.waitForTimeout(2000);

        // Klikneme na tlačítko pro plánování trasy
        console.log('🗺️ Hledám tlačítko pro plánování trasy...');

        try {
            // Zkusíme najít tlačítko trasy různými způsoby
            const routeSelectors = [
                'button[title*="trasa"]',
                'button[title*="Trasa"]',
                'button:has-text("Trasa")',
                '[data-testid*="route"]',
                '.route-button',
                'button[aria-label*="trasa"]'
            ];

            let routeButton = null;
            for (const selector of routeSelectors) {
                try {
                    routeButton = page.locator(selector).first();
                    if (await routeButton.count() > 0) {
                        console.log(`✅ Nalezeno tlačítko trasy: ${selector}`);
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }

            if (routeButton && await routeButton.count() > 0) {
                await routeButton.click();
                console.log('✅ Kliknuto na tlačítko trasy');
                await page.waitForTimeout(2000);
            } else {
                console.log('⚠️ Tlačítko trasy nenalezeno, zkusím přímý URL');
                await page.goto('https://mapy.cz/zakladni?planovac-tras=true');
                await page.waitForTimeout(3000);
            }

        } catch (error) {
            console.log('⚠️ Problém s tlačítkem trasy, zkusím alternativní přístup');
            await page.goto('https://mapy.cz/zakladni?planovac-tras=true');
            await page.waitForTimeout(3000);
        }

        console.log('\n📍 Začínám přidávat adresy do plánovače tras...');

        // Přidání adres do plánovače tras
        for (let i = 0; i < addresses.length; i++) {
            const addr = addresses[i];
            console.log(`   ${i + 1}/${addresses.length}: Přidávám ${addr.name} - ${addr.address}`);

            try {
                // Pro 3. adresu a každou další musíme nejdříve kliknout na tlačítko "+"
                if (i >= 2) {
                    console.log(`     🔘 Přidávám nové input pole (tlačítko +)...`);

                    const plusButtonSelectors = [
                        '.route-item-plus button.plus',
                        '.route-item-plus .plus',
                        'button.plus',
                        '.plus',
                        '[class*="plus"]',
                        'button:has-text("+")',
                        '[title*="přidat"]',
                        '[aria-label*="přidat"]'
                    ];

                    let plusButtonFound = false;
                    for (const selector of plusButtonSelectors) {
                        try {
                            const plusButton = page.locator(selector);
                            if (await plusButton.count() > 0) {
                                // Zkusíme najít viditelné tlačítko
                                const visibleButtons = await plusButton.all();
                                for (const button of visibleButtons) {
                                    if (await button.isVisible()) {
                                        await button.click();
                                        console.log(`     ✅ Kliknuto na tlačítko + (${selector})`);
                                        plusButtonFound = true;
                                        await page.waitForTimeout(1000);
                                        break;
                                    }
                                }
                                if (plusButtonFound) break;
                            }
                        } catch (e) {
                            continue;
                        }
                    }

                    if (!plusButtonFound) {
                        console.log(`     ⚠️ Tlačítko + nenalezeno, zkusím pokračovat...`);
                    }
                }

                // Hledáme input pole pro zadání adresy
                const inputSelectors = [
                    'input[placeholder*="adres"]',
                    'input[placeholder*="místo"]',
                    'input[placeholder*="hled"]',
                    'input[type="text"]',
                    '.search-input input',
                    '[data-testid*="search"] input',
                    '.route-input input',
                    '[class*="route"] input'
                ];

                let addressInput = null;
                for (const selector of inputSelectors) {
                    try {
                        const inputs = page.locator(selector);
                        const inputCount = await inputs.count();

                        if (inputCount > 0) {
                            // Pro první dvě adresy použijeme první dva inputy
                            // Pro další adresy použijeme poslední (nejnovější) input
                            let targetInput;
                            if (i < 2) {
                                targetInput = inputs.nth(i);
                            } else {
                                targetInput = inputs.last();
                            }

                            if (await targetInput.isVisible()) {
                                addressInput = targetInput;
                                console.log(`     🎯 Nalezen input (${selector}, index: ${i < 2 ? i : 'last'})`);
                                break;
                            }
                        }
                    } catch (e) {
                        continue;
                    }
                }

                if (addressInput) {
                    await addressInput.click();
                    await page.waitForTimeout(500);
                    await addressInput.fill(addr.address);
                    await page.waitForTimeout(1000);
                    await page.keyboard.press('Enter');
                    await page.waitForTimeout(2000);

                    console.log(`     ✅ Adresa "${addr.address}" přidána`);
                } else {
                    console.log(`     ⚠️ Input pole nenalezeno pro adresu: ${addr.address}`);
                }

            } catch (error) {
                console.log(`     ❌ Chyba při přidávání adresy ${addr.address}:`, error.message);
            }

            // Pauza mezi přidáváním adres
            await page.waitForTimeout(1500);
        }

        console.log('\n🎯 Všechny adresy přidány! Optimalizuji pořadí zastávek...');

        // Počkáme chvíli, aby se trasa vykreslila
        await page.waitForTimeout(3000);

        console.log('\n🎉 HOTOVO! Trasa je naplánována na mapy.cz');
        console.log('📋 Shrnutí:');
        console.log(`   • Zpracováno: ${addresses.length} adres`);
        console.log(`   • Prohlížeč zůstává otevřený pro kontrolu a úpravy`);
        console.log(`   • Můžete ručně optimalizovat trasu nebo přidat další body`);

        // Necháme prohlížeč otevřený pro uživatele
        console.log('\n⏳ Prohlížeč zůstane otevřený. Stiskněte Ctrl+C pro ukončení...');

        // Čekáme nekonečně dlouho, dokud uživatel neukončí
        await new Promise(() => {});

    } catch (error) {
        console.error('❌ Došlo k chybě při plánování trasy:', error.message);
    } finally {
        // Tato část se spustí pouze při ukončení
        console.log('🔚 Zavírám prohlížeč...');
        await browser.close();
    }
})();
