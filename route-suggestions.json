{"totalRoutes": 19, "totalAddresses": 155, "generatedAt": "2025-05-26T17:01:24.677Z", "routes": [{"id": 1, "name": "Strakonice - část 1", "city": "Strakonice", "addressCount": 8, "addresses": [{"id": 4, "name": "<PERSON><PERSON><PERSON>", "address": "46, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "46", "city": "Strakonice", "postCode": "386 01"}, {"id": 5, "name": "Drah<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1398, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Bahenní 1398", "city": "Strakonice", "postCode": "386 01"}, {"id": 6, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> Nímcov<PERSON> 1117, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Boženy Nímcové 1117", "city": "Strakonice", "postCode": "386 01"}, {"id": 7, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Dražejov90", "city": "Strakonice", "postCode": "386 01"}, {"id": 8, "name": "CZ004CST", "address": "Dukels<PERSON>á 33, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Dukelská 33", "city": "Strakonice", "postCode": "386 01"}, {"id": 9, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 167, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 167", "city": "Strakonice", "postCode": "386 01"}, {"id": 10, "name": "PTÁčEK - velkoobchod, a.s.", "address": "Katovick<PERSON> 1268 - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Katovická 1268 - IC", "city": "Strakonice", "postCode": "386 01"}, {"id": 11, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1307, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Katovická 1307", "city": "Strakonice", "postCode": "386 01"}]}, {"id": 2, "name": "Strakonice - část 2", "city": "Strakonice", "addressCount": 8, "addresses": [{"id": 12, "name": "AlzaBox Katovická", "address": "<PERSON><PERSON><PERSON><PERSON> 1404, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Katovická 1404", "city": "Strakonice", "postCode": "386 01"}, {"id": 13, "name": "<PERSON><PERSON><PERSON>", "address": "Kochana z Prachové119, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Kochana z Prachové119", "city": "Strakonice", "postCode": "386 01"}, {"id": 14, "name": "¼uboš Bandry", "address": "<PERSON><PERSON><PERSON><PERSON> 448, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Luční 448", "city": "Strakonice", "postCode": "386 01"}, {"id": 15, "name": "CZ003CST", "address": "<PERSON><PERSON><PERSON><PERSON> 1010, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Mírová 1010", "city": "Strakonice", "postCode": "386 01"}, {"id": 16, "name": "AlzaBox Na Ohradí", "address": "Na Ohradí 2, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Na Ohradí 2", "city": "Strakonice", "postCode": "386 01"}, {"id": 17, "name": "AlzaBox Písecká", "address": "<PERSON><PERSON>ecká, Strakonice, 386 01", "street": "Písecká", "city": "Strakonice", "postCode": "386 01"}, {"id": 18, "name": "<PERSON>", "address": "Radošovice  42, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Radošovice  42", "city": "Strakonice", "postCode": "386 01"}, {"id": 19, "name": "ELEKTRO S.M.S., spol. s r.o.Strakonice", "address": "řepice 136, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "řepice 136", "city": "Strakonice", "postCode": "386 01"}]}, {"id": 3, "name": "Strakonice - část 3", "city": "Strakonice", "addressCount": 8, "addresses": [{"id": 20, "name": "AlzaBox Smetanova", "address": "<PERSON><PERSON><PERSON><PERSON> 883, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Smetanova 883", "city": "Strakonice", "postCode": "386 01"}, {"id": 21, "name": "Ondř<PERSON>,", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 795, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Smidingerova 795", "city": "Strakonice", "postCode": "386 01"}, {"id": 22, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>207, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Stavbařů207", "city": "Strakonice", "postCode": "386 01"}, {"id": 23, "name": "<PERSON>,", "address": "štíkeň-<PERSON><PERSON><PERSON> 194, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "štíkeň-Rechle 194", "city": "Strakonice", "postCode": "386 01"}, {"id": 24, "name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>1152, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Tržní1152", "city": "Strakonice", "postCode": "386 01"}, {"id": 25, "name": "Soň<PERSON>", "address": "V Lipkách 104, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "V Lipkách 104", "city": "STRAKONICE", "postCode": "386 01"}, {"id": 26, "name": "Kasá<PERSON>y", "address": "V Lipkách100, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "V Lipkách100", "city": "Strakonice", "postCode": "386 01"}, {"id": 27, "name": "CZ005CST", "address": "<PERSON><PERSON><PERSON><PERSON> 140, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Velké námístí 140", "city": "Strakonice", "postCode": "386 01"}]}, {"id": 4, "name": "Strakonice - část 4", "city": "Strakonice", "addressCount": 8, "addresses": [{"id": 28, "name": "Centrum-Opravy a úpravy odívů", "address": "<PERSON><PERSON><PERSON><PERSON> 212, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Velké námístí 212", "city": "Strakonice", "postCode": "386 01"}, {"id": 29, "name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 49, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Velké námístí 49", "city": "Strakonice", "postCode": "386 01"}, {"id": 30, "name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 195, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Volyňská 195", "city": "Strakonice", "postCode": "386 01"}, {"id": 69, "name": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON> 1118, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON> 1118", "city": "Strakonice", "postCode": "386 01"}, {"id": 70, "name": "Mir<PERSON>", "address": "Baarova268 38601, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Baarova268 38601", "city": "Strakonice", "postCode": "386 01"}, {"id": 71, "name": "OLYMPIA PAPÍR s.r.o", "address": "Bezdíkovská 30, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Bezdíkovská 30", "city": "Strakonice", "postCode": "386 01"}, {"id": 72, "name": "<PERSON>", "address": "čejetice 136, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "čejetice 136", "city": "Strakonice", "postCode": "386 01"}, {"id": 73, "name": "<PERSON><PERSON><PERSON>", "address": "čejetice 70, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "čejetice 70", "city": "Strakonice", "postCode": "386 01"}]}, {"id": 5, "name": "Strakonice - část 5", "city": "Strakonice", "addressCount": 8, "addresses": [{"id": 74, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 1124, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "čelakovskeho 1124", "city": "Strakonice", "postCode": "386 01"}, {"id": 75, "name": "<PERSON>", "address": "čes<PERSON><PERSON><PERSON> 509, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "českých lesů 509", "city": "Strakonice", "postCode": "386 01"}, {"id": 76, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON> 31, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Domanice 31", "city": "Strakonice", "postCode": "386 01"}, {"id": 77, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1289, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1289", "city": "Strakonice", "postCode": "386 01"}, {"id": 78, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON> 321, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON> 321", "city": "Strakonice", "postCode": "386 01"}, {"id": 79, "name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 124, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Hranični 124", "city": "Strakonice", "postCode": "386 01"}, {"id": 80, "name": "<PERSON><PERSON><PERSON>", "address": "Chmelenského 167, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Chmelenského 167", "city": "Strakonice", "postCode": "386 01"}, {"id": 81, "name": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 293, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON>va 293", "city": "Strakonice", "postCode": "386 01"}]}, {"id": 6, "name": "Strakonice - část 6", "city": "Strakonice", "addressCount": 8, "addresses": [{"id": 82, "name": "OTAVANET, s.r.o.", "address": "<PERSON><PERSON><PERSON><PERSON> 175, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON> 175", "city": "Strakonice", "postCode": "386 01"}, {"id": 83, "name": "<PERSON><PERSON><PERSON>", "address": "Krty-Hrade<PERSON> 11, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Krty-Hradec 11", "city": "Strakonice", "postCode": "386 01"}, {"id": 84, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 11, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Mladíjovice 11", "city": "Strakonice", "postCode": "386 01"}, {"id": 85, "name": "Oldřich", "address": "<PERSON><PERSON><PERSON> 105, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON> 105", "city": "Strakonice", "postCode": "386 01"}, {"id": 86, "name": "Zdeňka Kocová", "address": "<PERSON><PERSON><PERSON><PERSON> 1109, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON>ého 1109", "city": "Strakonice", "postCode": "386 01"}, {"id": 87, "name": "<PERSON>", "address": "Na Muškách 625, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Na Muškách 625", "city": "Strakonice", "postCode": "386 01"}, {"id": 88, "name": "OX BOX Nádražní", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 337, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Nádražní 337", "city": "Strakonice", "postCode": "386 01"}, {"id": 89, "name": "AutomaCZ", "address": "Nebřehovice 39, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Nebřehovice 39", "city": "Strakonice", "postCode": "386 01"}]}, {"id": 7, "name": "Strakonice - část 7", "city": "Strakonice", "addressCount": 8, "addresses": [{"id": 90, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON> 632, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON> 632", "city": "Strakonice", "postCode": "386 01"}, {"id": 91, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 893, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Písecká 893", "city": "Strakonice", "postCode": "386 01"}, {"id": 92, "name": "<PERSON><PERSON><PERSON>", "address": "Pracejovice 14, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Pracejovice 14", "city": "Strakonice", "postCode": "386 01"}, {"id": 93, "name": "<PERSON>", "address": "Přes�ovice 48, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Přes�ovice 48", "city": "Strakonice", "postCode": "386 01"}, {"id": 94, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 430, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON> 430", "city": "Strakonice", "postCode": "386 01"}, {"id": 95, "name": "Nemocnice Strakonice", "address": "Radomyš<PERSON><PERSON><PERSON> 336, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Radomyšlská 336", "city": "Strakonice", "postCode": "386 01"}, {"id": 96, "name": "Libor <PERSON>", "address": "Sídliští 1. <PERSON><PERSON><PERSON> 1141, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Sídliští 1. máje 1141", "city": "Strakonice", "postCode": "386 01"}, {"id": 97, "name": "<PERSON><PERSON><PERSON>", "address": "Sousedovice 37, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Sousedovice 37", "city": "Strakonice", "postCode": "386 01"}]}, {"id": 8, "name": "Strakonice - část 8", "city": "Strakonice", "addressCount": 8, "addresses": [{"id": 98, "name": "MBM Westra s.r.o.", "address": "Sousedovice 61 61 / 61, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Sousedovice 61 61 / 61", "city": "Strakonice", "postCode": "386 01"}, {"id": 99, "name": "Zdeník špaček", "address": "<PERSON><PERSON><PERSON><PERSON>, 81, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON>usedov<PERSON>, 81", "city": "Strakonice", "postCode": "386 01"}, {"id": 100, "name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 209, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Stavbařů 209", "city": "Strakonice", "postCode": "386 01"}, {"id": 101, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 358, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "šumavská 358", "city": "Strakonice", "postCode": "386 01"}, {"id": 102, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON> 1152, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Trzni 1152", "city": "Strakonice", "postCode": "386 01"}, {"id": 103, "name": "<PERSON>", "address": "<PERSON> <PERSON> 793, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "V Raji 793", "city": "Strakonice", "postCode": "386 01"}, {"id": 104, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 460, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Vaclavska 460", "city": "Strakonice", "postCode": "386 01"}, {"id": 105, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON>el<PERSON> 9, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Velka Turna 9", "city": "Strakonice", "postCode": "386 01"}]}, {"id": 9, "name": "Strakonice - část 9", "city": "Strakonice", "addressCount": 3, "addresses": [{"id": 106, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON> 425, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON>kova 425", "city": "Strakonice", "postCode": "386 01"}, {"id": 153, "name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> č.ev. 102, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Dr<PERSON><PERSON><PERSON>ov č.ev. 102", "city": "Strakonice", "postCode": "386 01"}, {"id": 154, "name": "<PERSON>", "address": "<PERSON><PERSON> z Prachové  119, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Kochana z Prachové  119", "city": "Strakonice", "postCode": "386 01"}]}, {"id": 10, "name": "Vodňany - část 1", "city": "Vodňany", "addressCount": 8, "addresses": [{"id": 38, "name": "Len<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>  1327, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Bavorovská  1327", "city": "Vodňany", "postCode": "389 01"}, {"id": 39, "name": "<PERSON><PERSON>, spol. s r.o.", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>1284, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "číčenická1284", "city": "Vodňany", "postCode": "389 01"}, {"id": 40, "name": "AlzaBox Dr. <PERSON>", "address": "<PERSON><PERSON> 1285, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON>. <PERSON> 1285", "city": "Vodňany", "postCode": "389 01"}, {"id": 41, "name": "Elektro <PERSON>", "address": "Kalinovo nám. 40, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Kalinovo nám. 40", "city": "Vodňany", "postCode": "389 01"}, {"id": 42, "name": "WCOMP s.r.o.", "address": "nám<PERSON> <PERSON><PERSON><PERSON> 194, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "nám. Svobody 194", "city": "Vodňany", "postCode": "389 01"}, {"id": 43, "name": "<PERSON>ček", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 1214, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Stožická 1214", "city": "Vodňany", "postCode": "389 01"}, {"id": 126, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON> 135/I, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON> 135/I", "city": "Vodňany", "postCode": "389 01"}, {"id": 127, "name": "Frantisek <PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>  499, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON>  499", "city": "Vodňany", "postCode": "389 01"}]}, {"id": 11, "name": "Vodňany - část 2", "city": "Vodňany", "addressCount": 8, "addresses": [{"id": 128, "name": "<PERSON><PERSON>", "address": "čSLA 131, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "čSLA 131", "city": "Vodňany", "postCode": "389 01"}, {"id": 129, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1133, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Družstevní 1133", "city": "Vodňany", "postCode": "389 01"}, {"id": 130, "name": "EKOINSTAL CZ s.r.o.", "address": "<PERSON><PERSON><PERSON> 55, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Husova 55", "city": "Vodňany", "postCode": "389 01"}, {"id": 131, "name": "OX BOX Kampanova", "address": "<PERSON><PERSON><PERSON><PERSON>  591, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Kampanova  591", "city": "Vodňany", "postCode": "389 01"}, {"id": 132, "name": "Pivoňka Petr", "address": "Křepice 12, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Křepice 12", "city": "Vodňany", "postCode": "389 01"}, {"id": 133, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 70, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Křtítice 70", "city": "Vodňany", "postCode": "389 01"}, {"id": 134, "name": "<PERSON><PERSON><PERSON>", "address": "Lidmovice 8, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Lidmovice 8", "city": "Vodňany", "postCode": "389 01"}, {"id": 135, "name": "<PERSON><PERSON>", "address": "<PERSON><PERSON> 1211, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON> 1211", "city": "Vodňany", "postCode": "389 01"}]}, {"id": 12, "name": "Vodňany - část 3", "city": "Vodňany", "addressCount": 8, "addresses": [{"id": 136, "name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 958, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON><PERSON>va 958", "city": "Vodňany", "postCode": "389 01"}, {"id": 137, "name": "<PERSON>", "address": "Palackého 69 38901 / 69, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Palackého 69 38901 / 69", "city": "Vodňany", "postCode": "389 01"}, {"id": 138, "name": "<PERSON>", "address": "Radč<PERSON> 26, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Radčice 26", "city": "Vodňany", "postCode": "389 01"}, {"id": 139, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 346, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Radomilická 346", "city": "Vodňany", "postCode": "389 01"}, {"id": 140, "name": "Mateřská škola Vodňany, Smetanova 204", "address": "<PERSON><PERSON><PERSON><PERSON> 204, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Smetanova 204", "city": "Vodňany", "postCode": "389 01"}, {"id": 141, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 318, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "St<PERSON>žická 318", "city": "Vodňany", "postCode": "389 01"}, {"id": 142, "name": "Jan", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 690, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "St<PERSON>žická 690", "city": "Vodňany", "postCode": "389 01"}, {"id": 143, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1117, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Œumavska 1117", "city": "Vodňany", "postCode": "389 01"}]}, {"id": 13, "name": "Vodňany - část 4", "city": "Vodňany", "addressCount": 6, "addresses": [{"id": 144, "name": "<PERSON><PERSON><PERSON>K<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 272, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON><PERSON><PERSON> 272", "city": "Vodňany", "postCode": "389 01"}, {"id": 145, "name": "<PERSON><PERSON>", "address": "Výstavní 1038 1, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Výstavní 1038 1", "city": "Vodňany", "postCode": "389 01"}, {"id": 146, "name": "CZ001CST", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1043, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Výstavní 1043", "city": "Vodňany", "postCode": "389 01"}, {"id": 147, "name": "<PERSON><PERSON><PERSON> - POKR", "address": "<PERSON><PERSON><PERSON> 333, <PERSON><PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON><PERSON> 333", "city": "VODňANY", "postCode": "389 01"}, {"id": 148, "name": "<PERSON><PERSON><PERSON><PERSON>", "address": "Zizkovo namísti 161, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Zizkovo namísti 161", "city": "Vodňany", "postCode": "389 01"}, {"id": 155, "name": "Uloženka by WEDO - VM00770001-Pra<PERSON><PERSON><PERSON>", "address": "Drahonic<PERSON> 15, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Drahonice 15", "city": "Vodňany", "postCode": "389 01"}]}, {"id": 14, "name": "Radomyšl", "city": "Radomyšl", "addressCount": 6, "addresses": [{"id": 60, "name": "<PERSON><PERSON><PERSON>", "address": "Blatenská 62  CZ1, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Blatenská 62  CZ1", "city": "Radomyšl", "postCode": "387 31"}, {"id": 61, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON> 41, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON> 41", "city": "Radomyšl", "postCode": "386 01"}, {"id": 62, "name": "<PERSON><PERSON><PERSON>", "address": "Maltézské námístí 7, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Maltézské námístí 7", "city": "Radomyšl", "postCode": "387 31"}, {"id": 63, "name": "<PERSON><PERSON>", "address": "Na Trávník<PERSON> 225, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Na Trávníkách 225", "city": "Radomyšl", "postCode": "387 31"}, {"id": 64, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 172, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Sídliští 172", "city": "Radomyšl", "postCode": "387 31"}, {"id": 65, "name": "<PERSON><PERSON>", "address": "Za Humny 1, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Za Humny 1", "city": "Radomyšl", "postCode": "387 31"}]}, {"id": 15, "name": "Strakonice 1", "city": "Strakonice 1", "addressCount": 5, "addresses": [{"id": 32, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1280, <PERSON><PERSON><PERSON><PERSON> 1, 386 01", "street": "Jezerní 1280", "city": "Strakonice 1", "postCode": "386 01"}, {"id": 33, "name": "DANUšE SYNKOVÁ", "address": "<PERSON><PERSON><PERSON> 726, <PERSON><PERSON><PERSON><PERSON><PERSON> 1, 386 01", "street": "Profesora <PERSON> 726", "city": "STRAKONICE 1", "postCode": "386 01"}, {"id": 110, "name": "Lubos Potuznik", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1158, <PERSON><PERSON><PERSON><PERSON> 1, 386 01", "street": "Chelčickeho 1158", "city": "Strakonice 1", "postCode": "386 01"}, {"id": 111, "name": "<PERSON><PERSON><PERSON>", "address": "Radomylsk  336, Strakonice 1, 386 01", "street": "Radomylsk  336", "city": "Strakonice 1", "postCode": "386 01"}, {"id": 112, "name": "RENATA KOUBOVÁ", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 192, <PERSON><PERSON><PERSON><PERSON> 1, 386 01", "street": "Volyňská 192", "city": "Strakonice 1", "postCode": "386 01"}]}, {"id": 16, "name": "<PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>", "addressCount": 5, "addresses": [{"id": 37, "name": "<PERSON><PERSON>", "address": "Ra<PERSON>ilicka 410/2, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Radomilicka 410/2", "city": "<PERSON><PERSON><PERSON><PERSON>", "postCode": "389 01"}, {"id": 122, "name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON> 508 / 3, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON><PERSON> 508 / 3", "city": "<PERSON><PERSON><PERSON><PERSON>", "postCode": "389 01"}, {"id": 123, "name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 10, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Radcice 10", "city": "<PERSON><PERSON><PERSON><PERSON>", "postCode": "389 01"}, {"id": 124, "name": "Zdena Kvítková", "address": "<PERSON><PERSON><PERSON><PERSON> 69, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON><PERSON><PERSON> 69", "city": "<PERSON><PERSON><PERSON><PERSON>", "postCode": "389 01"}, {"id": 125, "name": "<PERSON><PERSON><PERSON>K<PERSON>", "address": "<PERSON><PERSON><PERSON> 272, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON>rsova 272", "city": "<PERSON><PERSON><PERSON><PERSON>", "postCode": "389 01"}]}, {"id": 17, "name": "Kombinovaná trasa: Katovice, Volenice", "city": "Kombinovaná", "addressCount": 7, "addresses": [{"id": 50, "name": "<PERSON>", "address": "28. <PERSON><PERSON><PERSON><PERSON> 85, <PERSON><PERSON><PERSON>, 387 11", "street": "28. <PERSON><PERSON><PERSON><PERSON> 85", "city": "Katovice", "postCode": "387 11"}, {"id": 51, "name": "<PERSON><PERSON><PERSON>,", "address": "Husovo namísti 10, <PERSON><PERSON><PERSON>, 387 11", "street": "Husovo namísti 10", "city": "Katovice", "postCode": "387 11"}, {"id": 52, "name": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 137, <PERSON><PERSON><PERSON>, 387 11", "street": "Nádražní 137", "city": "Katovice", "postCode": "387 11"}, {"id": 152, "name": "PENTA CZ, s.r.o. - PENTA CZ s.r.o.", "address": "Kosmetick<PERSON> 450, <PERSON><PERSON><PERSON>, 387 11", "street": "Kosmetická 450", "city": "Katovice", "postCode": "387 11"}, {"id": 44, "name": "<PERSON><PERSON>", "address": "Ohrazenice 5, <PERSON><PERSON><PERSON>, 387 16", "street": "Ohrazenice 5", "city": "Volenice", "postCode": "387 16"}, {"id": 45, "name": "<PERSON>", "address": "Volenice 124, <PERSON><PERSON><PERSON>, 387 16", "street": "Volenice 124", "city": "Volenice", "postCode": "387 16"}, {"id": 151, "name": "<PERSON><PERSON><PERSON><PERSON>", "address": "štíchovice 89, <PERSON><PERSON><PERSON>, 387 16", "street": "štíchovice 89", "city": "Volenice", "postCode": "387 16"}]}, {"id": 18, "name": "Kombinovaná trasa: <PERSON><PERSON><PERSON><PERSON><PERSON>, St<PERSON>els<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ra<PERSON>šov<PERSON>", "city": "Kombinovaná", "addressCount": 8, "addresses": [{"id": 3, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 84, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 72", "street": "Libíjovice 84", "city": "Libíjovice", "postCode": "387 72"}, {"id": 54, "name": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 83, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 72", "street": "Libíjovice 83", "city": "Libíjovice", "postCode": "387 72"}, {"id": 35, "name": "Jan", "address": "St<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Lhota 29, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Střelskohoštická Lhota 29", "city": "Střelské Hoš<PERSON>e", "postCode": "386 01"}, {"id": 117, "name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 148, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 15", "street": "Střelské <PERSON> 148", "city": "Střelské Hoš<PERSON>e", "postCode": "387 15"}, {"id": 58, "name": "<PERSON>", "address": "<PERSON> Rutaku 335, <PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Do Rutaku 335", "city": "Radomysl", "postCode": "387 31"}, {"id": 59, "name": "<PERSON><PERSON>,", "address": "Na Travnikach 225, <PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Na Travnikach 225", "city": "Radomysl", "postCode": "387 31"}, {"id": 66, "name": "<PERSON>", "address": "Radošovice 10, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Radošovice 10", "city": "Radošovice", "postCode": "386 01"}, {"id": 67, "name": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>108, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Radošovice108", "city": "Radošovice", "postCode": "386 01"}]}, {"id": 19, "name": "Kombinovaná trasa: Strakonice (okres strakonice), Strakonice strakonice i, Třebohostice", "city": "Kombinovaná", "addressCount": 6, "addresses": [{"id": 108, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 105, <PERSON><PERSON><PERSON><PERSON> (okres Strakonice), 386 01", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 105", "city": "Strakonice (okres Strakonice)", "postCode": "386 01"}, {"id": 109, "name": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON>, 187, <PERSON><PERSON><PERSON><PERSON> (okres Strakonice), 386 01", "street": "<PERSON><PERSON><PERSON><PERSON>, 187", "city": "Strakonice (okres Strakonice)", "postCode": "386 01"}, {"id": 113, "name": "<PERSON>", "address": "Baarova 1062, Strakonice Strakonice I, 386 01", "street": "Baarova 1062", "city": "Strakonice Strakonice I", "postCode": "386 01"}, {"id": 114, "name": "Basedesign, <PERSON>", "address": "Lidick<PERSON> 300, Strakonice Strakonice I, 386 01", "street": "Lidická 300", "city": "Strakonice Strakonice I", "postCode": "386 01"}, {"id": 119, "name": "<PERSON>", "address": "Třebohostice 12, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 387 37", "street": "Třebohostice 12", "city": "Třebohostice", "postCode": "387 37"}, {"id": 120, "name": "<PERSON><PERSON><PERSON><PERSON>", "address": "Třebohostice 45, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 387 37", "street": "Třebohostice 45", "city": "Třebohostice", "postCode": "387 37"}]}]}