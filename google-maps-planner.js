const { chromium } = require('playwright');
const fs = require('fs');

// Nastavení kódování pro správné zobrazení češtiny
process.stdout.setEncoding('utf8');

console.log('🗺️ Google Maps plánovač tras s detailními informacemi');
console.log('====================================================\n');

// Funkce pro vytvoření detailního popisu bodu
function createPointDescription(addr) {
    // Formát: "Název firmy/jméno - Jméno osoby" nebo jen "J<PERSON>no osoby"
    let description = '';
    
    if (addr.name && addr.name.trim()) {
        description = addr.name.trim();
    }
    
    // Pokud máme dodatečné informace z původních dat, p<PERSON>id<PERSON><PERSON> je
    if (addr.originalData) {
        const surname = addr.originalData['Recipient Surname'] || '';
        const companyName = addr.originalData['Recipient Company Name'] || '';
        
        if (companyName && companyName !== addr.name) {
            if (surname && surname !== addr.name) {
                description = `${companyName} - ${surname}`;
            } else {
                description = companyName;
            }
        } else if (surname && surname !== addr.name) {
            description = `${addr.name} - ${surname}`;
        }
    }
    
    return description || addr.name || 'Neznámý příjemce';
}

(async () => {
    try {
        // Načtení opravených adres
        console.log('📂 Načítám opravené adresy...');
        
        let data;
        try {
            data = JSON.parse(fs.readFileSync('extracted-addresses-corrected.json', 'utf8'));
            console.log('✅ Použity opravené adresy s správnou češtinou');
        } catch (e) {
            console.log('⚠️ Opravené adresy nenalezeny, používám původní...');
            data = JSON.parse(fs.readFileSync('extracted-addresses.json', 'utf8'));
        }
        
        const addresses = data.addresses;
        console.log(`📊 Celkem adres: ${addresses.length}`);
        
        // Omezíme na prvních 10 adres pro test (Google Maps má limit)
        const testAddresses = addresses.slice(0, 10);
        console.log(`🧪 Testujeme s prvními ${testAddresses.length} adresami\n`);
        
        // Zobrazíme adresy, které budeme přidávat
        console.log('📍 Adresy k přidání:');
        console.log('=====================================');
        testAddresses.forEach((addr, index) => {
            const description = createPointDescription(addr);
            console.log(`${index + 1}. ${description}`);
            console.log(`   📍 ${addr.address}`);
            console.log('');
        });
        
        // Spuštění prohlížeče
        console.log('🌐 Spouštím prohlížeč a otevírám Google Maps...');
        const browser = await chromium.launch({ 
            headless: false,
            slowMo: 300
        });
        
        const context = await browser.newContext();
        const page = await context.newPage();
        
        // Otevření Google Maps
        console.log('📍 Otevírám Google Maps...');
        await page.goto('https://maps.google.com', { waitUntil: 'networkidle' });
        await page.waitForTimeout(3000);
        
        // Zavřeme případné cookies dialogy
        try {
            const acceptButton = page.locator('button:has-text("Přijmout vše"), button:has-text("Accept all"), button:has-text("I agree")').first();
            if (await acceptButton.count() > 0) {
                await acceptButton.click();
                console.log('✅ Cookies přijaty');
                await page.waitForTimeout(1000);
            }
        } catch (e) {
            console.log('ℹ️ Cookies dialog nenalezen');
        }
        
        // Klikneme na tlačítko "Trasy" nebo "Directions"
        console.log('🗺️ Aktivuji plánovač tras...');
        
        try {
            const directionsSelectors = [
                'button[data-value="Directions"]',
                'button[aria-label*="Directions"]',
                'button[aria-label*="Trasy"]',
                '[data-testid="directions"]',
                'button:has-text("Directions")',
                'button:has-text("Trasy")'
            ];
            
            let directionsButton = null;
            for (const selector of directionsSelectors) {
                try {
                    directionsButton = page.locator(selector).first();
                    if (await directionsButton.count() > 0 && await directionsButton.isVisible()) {
                        await directionsButton.click();
                        console.log(`✅ Plánovač tras aktivován (${selector})`);
                        await page.waitForTimeout(2000);
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }
            
            if (!directionsButton || await directionsButton.count() === 0) {
                // Alternativní přístup - přímý URL
                console.log('⚠️ Tlačítko tras nenalezeno, zkouším přímý URL...');
                await page.goto('https://maps.google.com/maps/dir/', { waitUntil: 'networkidle' });
                await page.waitForTimeout(3000);
                console.log('✅ Plánovač tras otevřen přímým URL');
            }
            
        } catch (error) {
            console.log('⚠️ Problém s aktivací plánovače tras, pokračuji...');
        }
        
        console.log('\n📍 Začínám přidávat adresy s detailními informacemi...');
        
        // Funkce pro hledání input polí
        async function findAddressInputs() {
            const inputSelectors = [
                'input[placeholder*="destination"]',
                'input[placeholder*="Choose destination"]',
                'input[placeholder*="Vyberte cíl"]',
                'input[aria-label*="destination"]',
                'input[aria-label*="Destination"]',
                'div[data-testid*="directions"] input',
                '.directions-searchbox input',
                'input[type="text"]'
            ];
            
            for (const selector of inputSelectors) {
                const inputs = page.locator(selector);
                const count = await inputs.count();
                if (count > 0) {
                    console.log(`     🔍 Nalezeno ${count} input polí (${selector})`);
                    return inputs;
                }
            }
            return null;
        }
        
        // Funkce pro přidání dalšího bodu
        async function addDestination() {
            const addButtonSelectors = [
                'button[aria-label*="Add destination"]',
                'button[aria-label*="Přidat cíl"]',
                'button:has-text("+")',
                '[data-testid*="add-destination"]',
                'button[title*="Add destination"]',
                '.add-destination-button'
            ];
            
            for (const selector of addButtonSelectors) {
                try {
                    const button = page.locator(selector);
                    if (await button.count() > 0 && await button.isVisible()) {
                        await button.click();
                        console.log(`     ✅ Přidán nový cíl (${selector})`);
                        await page.waitForTimeout(1500);
                        return true;
                    }
                } catch (e) {
                    continue;
                }
            }
            return false;
        }
        
        // Přidání adres
        for (let i = 0; i < testAddresses.length; i++) {
            const addr = testAddresses[i];
            const description = createPointDescription(addr);
            
            console.log(`\n   ${i + 1}/${testAddresses.length}: Přidávám bod`);
            console.log(`   👤 ${description}`);
            console.log(`   📍 ${addr.address}`);
            
            try {
                // Pro 3. adresu a další musíme přidat nový cíl
                if (i >= 2) {
                    console.log(`     🔘 Přidávám nový cíl...`);
                    const added = await addDestination();
                    if (!added) {
                        console.log(`     ⚠️ Nepodařilo se přidat nový cíl`);
                    }
                }
                
                // Najdeme dostupná input pole
                const inputs = await findAddressInputs();
                if (!inputs) {
                    console.log(`     ❌ Input pole nenalezena`);
                    continue;
                }
                
                const inputCount = await inputs.count();
                let targetInput;
                
                if (i < inputCount) {
                    targetInput = inputs.nth(i);
                } else {
                    targetInput = inputs.last();
                }
                
                if (await targetInput.isVisible()) {
                    console.log(`     ⌨️ Vyplňuji adresu...`);
                    
                    // Vyčistíme pole a vyplníme adresu
                    await targetInput.click();
                    await page.waitForTimeout(300);
                    await targetInput.selectText();
                    
                    // Vytvoříme kompletní text s popisem
                    const fullText = `${description}, ${addr.address}`;
                    await targetInput.fill(fullText);
                    await page.waitForTimeout(1000);
                    await page.keyboard.press('Enter');
                    await page.waitForTimeout(3000);
                    
                    console.log(`     ✅ Přidán bod: ${description}`);
                    console.log(`     📍 Adresa: ${addr.address}`);
                } else {
                    console.log(`     ❌ Input pole není dostupné`);
                }
                
            } catch (error) {
                console.log(`     ❌ Chyba při přidávání bodu: ${error.message}`);
            }
        }
        
        console.log('\n🎯 Přidávání bodů dokončeno!');
        
        // Hledáme možnosti optimalizace
        console.log('\n🔍 Hledám možnosti optimalizace trasy...');
        await page.waitForTimeout(3000);
        
        try {
            const optimizeSelectors = [
                'button[aria-label*="Optimize"]',
                'button[aria-label*="optimalizovat"]',
                'button:has-text("Optimize route")',
                'button:has-text("Optimalizovat trasu")',
                '[data-testid*="optimize"]',
                'button[title*="optimize"]'
            ];
            
            let optimizeFound = false;
            for (const selector of optimizeSelectors) {
                try {
                    const button = page.locator(selector);
                    if (await button.count() > 0 && await button.isVisible()) {
                        console.log(`✅ Nalezeno tlačítko optimalizace: ${selector}`);
                        await button.click();
                        console.log(`🚀 Optimalizace trasy aktivována!`);
                        optimizeFound = true;
                        await page.waitForTimeout(3000);
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }
            
            if (!optimizeFound) {
                console.log('ℹ️ Automatická optimalizace nenalezena');
                console.log('💡 Zkuste ručně:');
                console.log('   1. Hledejte menu nebo nastavení tras');
                console.log('   2. Přetáhněte body myší pro změnu pořadí');
                console.log('   3. Hledejte možnost "Optimize route order"');
            }
            
        } catch (error) {
            console.log(`⚠️ Chyba při hledání optimalizace: ${error.message}`);
        }
        
        console.log('\n📊 Shrnutí:');
        console.log('=====================================');
        console.log(`✅ Přidáno ${testAddresses.length} bodů na Google Maps`);
        console.log('✅ Každý bod obsahuje jméno a adresu');
        console.log('✅ Správné české znaky (Chelčice, Němcové, atd.)');
        console.log('✅ Připraveno pro optimalizaci tras');
        
        console.log('\n⏳ Prohlížeč zůstane otevřený pro ruční úpravy...');
        console.log('💡 Můžete:');
        console.log('   • Přetáhnout body pro změnu pořadí');
        console.log('   • Hledat možnosti optimalizace');
        console.log('   • Přidat další body ručně');
        console.log('   • Uložit trasu do Google účtu');
        
        // Čekáme nekonečně dlouho
        await new Promise(() => {});
        
    } catch (error) {
        console.error('❌ Došlo k chybě:', error.message);
    }
})();
