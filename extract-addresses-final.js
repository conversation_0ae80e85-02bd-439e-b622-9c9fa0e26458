const XLSX = require('xlsx');
const fs = require('fs');

// Nastavení kódování pro správné zobrazení češtiny
process.stdout.setEncoding('utf8');

console.log('📊 Finální extraktor adres - bez úprav kódování');
console.log('=================================================\n');

try {
    // Načtení Excel souboru s různými možnostmi kódování
    console.log('📂 Načítám soubor souradnice.xls...');
    
    // Zkusíme různé možnosti načtení
    let workbook;
    const fileBuffer = fs.readFileSync('souradnice.xls');
    
    // Možnost 1: Základní načtení
    try {
        workbook = XLSX.read(fileBuffer, { 
            type: 'buffer',
            cellText: false,
            cellDates: true,
            raw: true
        });
        console.log('✅ Soubor načten - základní režim');
    } catch (e) {
        console.log('⚠️ Základní načtení selhalo, zkouším alternativy...');
        workbook = XLSX.read(fileBuffer, { type: 'buffer' });
    }
    
    // Získáme první list
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    console.log(`📋 Název listu: ${sheetName}`);
    
    // Převedeme na JSON bez jakýchkoli úprav
    const data = XLSX.utils.sheet_to_json(worksheet, {
        raw: false, // Převede na text
        defval: '', // Výchozí hodnota pro prázdné buňky
        blankrows: false // Přeskočí prázdné řádky
    });
    
    console.log(`📊 Celkem řádků: ${data.length}`);
    
    // Zobrazíme strukturu dat (první 3 řádky) - bez jakýchkoli úprav
    console.log('\n🔍 Struktura dat (první 3 řádky) - RAW:');
    console.log('=====================================\n');
    
    data.slice(0, 3).forEach((row, index) => {
        console.log(`Řádek ${index + 1}:`);
        Object.keys(row).forEach(key => {
            const value = row[key];
            if (value && value.toString().trim()) {
                console.log(`  ${key}: ${value}`);
            }
        });
        console.log('');
    });
    
    console.log('📍 Extrakce adres - BEZ ÚPRAV KÓDOVÁNÍ...');
    console.log('=====================================');
    
    // Extrakce adres - ŽÁDNÉ úpravy kódování
    const addresses = [];
    const uniqueAddresses = new Set();
    
    data.forEach((row, index) => {
        // Hledáme sloupce s adresními údaji - BEZ jakýchkoli úprav
        const street = row['Recipient Street'] || row['Ulice'] || row['Street'] || '';
        const city = row['Recipient City'] || row['Město'] || row['City'] || '';
        const postCode = row['Recipient Post Code'] || row['PSČ'] || row['PostCode'] || '';
        const name = row['Recipient Name'] || row['Recipient Company Name'] || row['Jméno'] || row['Name'] || `Místo ${index + 1}`;
        
        // Sestavení adresy - BEZ jakýchkoli úprav
        let fullAddress = '';
        if (street && street.toString().trim()) {
            fullAddress += street.toString().trim();
        }
        if (city && city.toString().trim()) {
            if (fullAddress) fullAddress += ', ';
            fullAddress += city.toString().trim();
        }
        if (postCode && postCode.toString().trim()) {
            if (fullAddress) fullAddress += ', ';
            fullAddress += postCode.toString().trim();
        }
        
        // Přidáme pouze platné a unikátní adresy - BEZ úprav
        if (fullAddress.length > 10 && !uniqueAddresses.has(fullAddress)) {
            uniqueAddresses.add(fullAddress);
            addresses.push({
                id: addresses.length + 1,
                name: name.toString().trim(),
                address: fullAddress,
                street: street.toString().trim(),
                city: city.toString().trim(),
                postCode: postCode.toString().trim()
            });
        }
    });
    
    console.log(`✅ Nalezeno ${addresses.length} unikátních adres\n`);
    
    // Zobrazení prvních 10 adres pro kontrolu kódování
    console.log('📋 Prvních 10 adres (RAW - bez úprav):');
    console.log('=====================================');
    addresses.slice(0, 10).forEach((addr, index) => {
        console.log(`${index + 1}. ${addr.name}`);
        console.log(`   📍 ${addr.address}`);
        if (index < addresses.length - 1) console.log('');
    });
    
    // Uložení do JSON souboru
    const outputData = {
        totalAddresses: addresses.length,
        extractedAt: new Date().toISOString(),
        encoding: 'RAW - bez úprav',
        note: 'Data extrahována bez jakýchkoli úprav kódování',
        addresses: addresses
    };
    
    // Uložení s UTF-8 kódováním
    fs.writeFileSync('extracted-addresses-final.json', JSON.stringify(outputData, null, 2), { encoding: 'utf8' });
    console.log('\n💾 Adresy uloženy do souboru: extracted-addresses-final.json');
    
    // Vytvoření jednoduchého seznamu
    const simpleList = addresses.map(addr => addr.address).join('\n');
    fs.writeFileSync('addresses-list-final.txt', simpleList, { encoding: 'utf8' });
    console.log('📝 Jednoduchý seznam uložen do: addresses-list-final.txt');
    
    // Vytvoření seznamu s názvy
    const detailedList = addresses.map((addr, index) => 
        `${index + 1}. ${addr.name}\n   📍 ${addr.address}`
    ).join('\n\n');
    fs.writeFileSync('addresses-detailed-final.txt', detailedList, { encoding: 'utf8' });
    console.log('📋 Detailní seznam uložen do: addresses-detailed-final.txt');
    
    // Analýza kódování
    console.log('\n🔍 Analýza kódování:');
    console.log('=====================================');
    
    // Hledáme specifické problematické vzory
    const patterns = {
        'Chelčice': 0,
        'Chelčice': 0,
        'Němcové': 0,
        'Nímcové': 0,
        'Nìmcové': 0,
        'ě': 0,
        'č': 0,
        'ř': 0,
        'š': 0,
        'ž': 0,
        'ý': 0,
        'á': 0,
        'í': 0,
        'é': 0,
        'ú': 0,
        'ů': 0,
        'ť': 0,
        'ď': 0,
        'ň': 0
    };
    
    addresses.forEach(addr => {
        const text = `${addr.name} ${addr.address}`.toLowerCase();
        Object.keys(patterns).forEach(pattern => {
            const count = (text.match(new RegExp(pattern.toLowerCase(), 'g')) || []).length;
            patterns[pattern] += count;
        });
    });
    
    console.log('Nalezené vzory:');
    Object.entries(patterns).forEach(([pattern, count]) => {
        if (count > 0) {
            console.log(`  ${pattern}: ${count}x`);
        }
    });
    
    // Hledáme konkrétní problematické adresy
    console.log('\n🔍 Kontrola konkrétních adres:');
    console.log('=====================================');
    
    const problematicAddresses = addresses.filter(addr => 
        addr.address.includes('Chelčice') || 
        addr.address.includes('Nímcové') ||
        addr.address.includes('Nìmcové') ||
        addr.name.includes('Chelčice') ||
        addr.name.includes('Nímcové') ||
        addr.name.includes('Nìmcové')
    );
    
    if (problematicAddresses.length > 0) {
        console.log('Nalezeny problematické adresy:');
        problematicAddresses.forEach((addr, index) => {
            console.log(`${index + 1}. ${addr.name}`);
            console.log(`   📍 ${addr.address}`);
        });
    } else {
        console.log('✅ Žádné zjevně problematické adresy nenalezeny');
    }
    
    console.log('\n🎯 HOTOVO!');
    console.log('=====================================');
    console.log('Vytvořené soubory (RAW data):');
    console.log('• extracted-addresses-final.json - všechny adresy');
    console.log('• addresses-list-final.txt - jednoduchý seznam');
    console.log('• addresses-detailed-final.txt - detailní seznam');
    console.log('\nData jsou extrahována BEZ jakýchkoli úprav kódování.');
    console.log('Pokud jsou znaky stále špatně, problém je v původním Excel souboru.');
    
} catch (error) {
    console.error('❌ Chyba při zpracování:', error.message);
    console.log('\n💡 Možná řešení:');
    console.log('1. Zkontrolujte, že soubor souradnice.xls existuje');
    console.log('2. Zkuste uložit Excel soubor jako CSV s UTF-8 kódováním');
    console.log('3. Nebo otevřete Excel a uložte znovu s českým kódováním');
}
