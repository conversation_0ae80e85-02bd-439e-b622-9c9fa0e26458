{"totalAddresses": 155, "extractedAt": "2025-05-26T18:29:16.675Z", "encoding": "Opraveno pro Google Maps", "format": "Název/Firma - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "addresses": [{"id": 1, "displayName": "OX BOX Chelčice", "personName": "olga nová", "googleMapsLabel": "OX BOX Chelčice - olga nová", "companyName": "OX BOX Chelčice", "recipientName": "", "surname": "olga nová", "address": "Chelčice 5, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Chelčice 5", "city": "Chelčice", "postCode": "389 01", "phone": "+420777862314", "fullGoogleMapsText": "OX BOX Chelčice - olga nová, Chelčice 5, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09191103", "Smìr": "S220602", "DT": "", "Recipient Company Name": "OX BOX Chelèice", "Recipient Name": "", "Recipient Surname": "olga nová", "Recipient Street": "Chelèice 5", "Recipient Post Code": "389 01", "Recipient City": "Chelèice", "Recipient Phone GSM": "+420777862314", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 2, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON> 55, <PERSON><PERSON>, 386 01", "street": "<PERSON>ín 55", "city": "<PERSON><PERSON>", "postCode": "386 01", "phone": "+420722960264", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> 55, <PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09186548", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "<PERSON>ín 55", "Recipient Post Code": "386 01", "Recipient City": "<PERSON><PERSON>", "Recipient Phone GSM": "+420722960264", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 3, "displayName": "Pazdera Jiří", "personName": "<PERSON><PERSON><PERSON>", "googleMapsLabel": "Pazdera Jiří - <PERSON><PERSON><PERSON>", "companyName": "Pazdera Jiří", "recipientName": "<PERSON><PERSON><PERSON>", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 84, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 72", "street": "Libějovice 84", "city": "Libějovice", "postCode": "387 72", "phone": "00420605297185", "fullGoogleMapsText": "Pazdera Jiří - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 84, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 72", "originalData": {"Order Number 1": "87T00071032", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON><PERSON>", "Recipient Surname": "", "Recipient Street": "Libìjovice 84", "Recipient Post Code": "387 72", "Recipient City": "Libìjovice", "Recipient Phone GSM": "00420605297185", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "1", "Box count": "1", "Pùvodní smìr": "S440700", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 4, "displayName": "<PERSON><PERSON><PERSON>", "personName": "<PERSON><PERSON>", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON>", "address": "46, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "46", "city": "Strakonice", "postCode": "386 01", "phone": "+420723539104", "fullGoogleMapsText": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, 46, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "45T00088031", "Smìr": "8224614", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON><PERSON>", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "46", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420723539104", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "50", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "251"}}, {"id": 5, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "personName": "Hálová", "googleMapsLabel": "<PERSON><PERSON><PERSON><PERSON>lov<PERSON>", "companyName": "<PERSON><PERSON><PERSON><PERSON>", "recipientName": "Drah<PERSON><PERSON>", "surname": "Hálová", "address": "<PERSON><PERSON><PERSON><PERSON> 1398, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Bahenní 1398", "city": "Strakonice", "postCode": "386 01", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1398, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1H200001844", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Name": "Drah<PERSON><PERSON>", "Recipient Surname": "Hálová", "Recipient Street": "Bahenní 1398", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "23", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 6, "displayName": "<PERSON>", "personName": "Ho<PERSON>ová", "googleMapsLabel": "<PERSON> - Ho<PERSON>ová", "companyName": "<PERSON>", "recipientName": "<PERSON>", "surname": "Ho<PERSON>ová", "address": "<PERSON><PERSON><PERSON><PERSON> Ně<PERSON>v<PERSON> 1117, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Boženy Němcové 1117", "city": "Strakonice", "postCode": "386 01", "phone": "+420606636880", "fullGoogleMapsText": "<PERSON> - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Ně<PERSON> 1117, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "09M00019605", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "<PERSON>", "Recipient Surname": "Ho<PERSON>ová", "Recipient Street": "Boženy Nìmcové 1117", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420606636880", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 7, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Dražejov90", "city": "Strakonice", "postCode": "386 01", "phone": "+420734357069", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1X820354950", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Dražejov90", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420734357069", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.03", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 8, "displayName": "CZ004CST", "personName": "<PERSON><PERSON>", "googleMapsLabel": "CZ004CST - <PERSON><PERSON>", "companyName": "CZ004CST", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "Dukels<PERSON>á 33, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Dukelská 33 ", "city": "Strakonice", "postCode": "386 01", "phone": "+420607065695", "fullGoogleMapsText": "CZ004CST - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 33, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09184140", "Smìr": "S220500", "DT": "", "Recipient Company Name": "CZ004CST", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Dukelská 33 ", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420607065695", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 9, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 167, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 167", "city": "Strakonice", "postCode": "386 01", "phone": "00420*********", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 167, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "77B03602338", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 167", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "00420*********", "Poznámky": "Tel. avizo: 00420*********", "Poznámky zak servis": "", "Total Weight": "20.36", "Total Volume": "0.118", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 10, "displayName": "PTÁČEK - velkoobchod, a.s.", "personName": "", "googleMapsLabel": "PTÁČEK - velkoobchod, a.s.", "companyName": "PTÁČEK - velkoobchod, a.s.", "recipientName": "", "surname": "", "address": "Katovick<PERSON> 1268 - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Katovická 1268 - IC", "city": "Strakonice", "postCode": "386 01", "phone": "", "fullGoogleMapsText": "PTÁČEK - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a.s., Katovická 1268 - IC, Strak<PERSON>e, 386 01", "originalData": {"Order Number 1": "2X700046063", "Smìr": "S220500", "DT": "", "Recipient Company Name": "PTÁÈEK - velkoobchod, a.s.", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Katovická 1268 - IC", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "2.13", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 11, "displayName": "<PERSON><PERSON><PERSON>", "personName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "googleMapsLabel": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1307, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Katovická 1307", "city": "Strakonice", "postCode": "386 01", "phone": "+420606843920", "fullGoogleMapsText": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1307, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "B1200169929", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON><PERSON>", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "Katovická 1307", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420606843920", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "19", "Total Volume": "0", "Box count": "2", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 12, "displayName": "AlzaBox Katovická", "personName": "Lenka Slavíčková", "googleMapsLabel": "AlzaBox Katovická - Lenka Slavíčková", "companyName": "AlzaBox Katovická", "recipientName": "", "surname": "Lenka Slavíčková", "address": "<PERSON><PERSON><PERSON><PERSON> 1404, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Katovická 1404", "city": "Strakonice", "postCode": "386 01", "phone": "420739755746", "fullGoogleMapsText": "AlzaBox Katovická - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1404, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09187240", "Smìr": "S220500", "DT": "", "Recipient Company Name": "AlzaBox Katovická", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Katovická 1404", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "420739755746", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 13, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "", "address": "Kochana z Prachové119, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Kochana z Prachové119", "city": "Strakonice", "postCode": "386 01", "phone": "+420722596652", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>119, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1X820354917", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Kochana z Prachové119", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420722596652", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.03", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 14, "displayName": "¼uboš Bandry", "personName": "", "googleMapsLabel": "¼uboš Bandry", "companyName": "¼uboš Bandry", "recipientName": "¼uboš Bandry", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON> 448, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Luční 448", "city": "Strakonice", "postCode": "386 01", "phone": "+420723785332", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 448, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "7K700104747", "Smìr": "S220500", "DT": "", "Recipient Company Name": "¼uboš Bandry", "Recipient Name": "¼uboš Bandry", "Recipient Surname": "", "Recipient Street": "Luèní 448", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420723785332", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "30", "Total Volume": "0", "Box count": "2", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 15, "displayName": "CZ003CST", "personName": "<PERSON>", "googleMapsLabel": "CZ003CST - <PERSON>", "companyName": "CZ003CST", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1010, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Mírová 1010 ", "city": "Strakonice", "postCode": "386 01", "phone": "+420606732965", "fullGoogleMapsText": "CZ003CST - <PERSON>, <PERSON><PERSON><PERSON><PERSON> 1010, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09188884", "Smìr": "S220500", "DT": "", "Recipient Company Name": "CZ003CST", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Mírová 1010 ", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420606732965", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 16, "displayName": "AlzaBox Na Ohradě", "personName": "<PERSON>", "googleMapsLabel": "AlzaBox Na Ohradě - <PERSON>", "companyName": "AlzaBox Na Ohradě", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON> Ohradě 2, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Na Ohradě 2", "city": "Strakonice", "postCode": "386 01", "phone": "+420607783283", "fullGoogleMapsText": "AlzaBox Na Ohradě - <PERSON>, <PERSON> 2, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09184371", "Smìr": "S220500", "DT": "", "Recipient Company Name": "AlzaBox Na Ohradì", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Na Ohradì 2", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420607783283", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 17, "displayName": "AlzaBox Písecká", "personName": "<PERSON><PERSON><PERSON><PERSON>", "googleMapsLabel": "AlzaBox Písecká - <PERSON><PERSON><PERSON><PERSON>", "companyName": "AlzaBox Písecká", "recipientName": "", "surname": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON>ecká, Strakonice, 386 01", "street": "Písecká ", "city": "Strakonice", "postCode": "386 01", "phone": "+420774343499", "fullGoogleMapsText": "AlzaBox Písecká - <PERSON><PERSON><PERSON><PERSON>, P<PERSON>eck<PERSON>, Strakonice, 386 01", "originalData": {"Order Number 1": "1AM09187851", "Smìr": "S220500", "DT": "", "Recipient Company Name": "AlzaBox Písecká", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "Písecká ", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420774343499", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 18, "displayName": "<PERSON>", "personName": "<PERSON><PERSON><PERSON><PERSON>", "googleMapsLabel": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "companyName": "<PERSON>", "recipientName": "<PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "address": "Radošovice  42, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Radošovice  42", "city": "Strakonice", "postCode": "386 01", "phone": "", "fullGoogleMapsText": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>  42, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "04G00017808", "Smìr": "8224614", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "<PERSON>", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "Radošovice  42", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "", "Poznámky": "Textil(Obleèení) - 0605-2430-1022-5890", "Poznámky zak servis": "<PERSON><PERSON><PERSON><PERSON>, opìtovné vyzvednutí 31.07.2024", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "204"}}, {"id": 19, "displayName": "ELEKTRO S.M.S., spol. s r.o.Strakonice", "personName": "", "googleMapsLabel": "ELEKTRO S.M.S., spol. s r.o.Strakonice", "companyName": "ELEKTRO S.M.S., spol. s r.o.Strakonice", "recipientName": "", "surname": "", "address": "Řepice 136, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Řepice 136", "city": "Strakonice", "postCode": "386 01", "phone": "+420383322474", "fullGoogleMapsText": "ELEKTRO S.M.S., spol. s r.<PERSON><PERSON>, Řepice 136, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "4L500011661", "Smìr": "S220500", "DT": "", "Recipient Company Name": "ELEKTRO S.M.S., spol. s r.o.Strakonice", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Øepice 136", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420383322474", "Poznámky": "", "Poznámky zak servis": "Smìrovaní zásilky zpìt", "Total Weight": "10", "Total Volume": "0.085", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "1"}}, {"id": 20, "displayName": "AlzaBox Smetanova", "personName": "<PERSON><PERSON><PERSON>", "googleMapsLabel": "AlzaBox Smetanova - <PERSON><PERSON><PERSON>", "companyName": "AlzaBox Smetanova", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 883, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Smetanova 883", "city": "Strakonice", "postCode": "386 01", "phone": "+420736162719", "fullGoogleMapsText": "AlzaBox Smetanova - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 883, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09194315", "Smìr": "S220500", "DT": "", "Recipient Company Name": "AlzaBox Smetanova", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Smetanova 883", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420736162719", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 21, "displayName": "Ondř<PERSON>,", "personName": "", "googleMapsLabel": "Ondř<PERSON>,", "companyName": "Ondř<PERSON>,", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 795, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Smidingerova 795", "city": "Strakonice", "postCode": "386 01", "phone": "+420722727888", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON>,, <PERSON><PERSON><PERSON><PERSON><PERSON> 795, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "4RA00063572", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON><PERSON>,", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Smidingerova 795", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420722727888", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "2", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 22, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>207, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Stavbařů207", "city": "Strakonice", "postCode": "386 01", "phone": "+420605260940", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>207, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1X820354902", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Stavbaøù207", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420605260940", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.03", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 23, "displayName": "<PERSON>,", "personName": "", "googleMapsLabel": "<PERSON>,", "companyName": "<PERSON>,", "recipientName": "", "surname": "", "address": "Štěkeň-<PERSON><PERSON><PERSON> 194, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Štěkeň-Rechle 194", "city": "Strakonice", "postCode": "386 01", "phone": "+420606748986", "fullGoogleMapsText": "<PERSON>,, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 194, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "4RA00065338", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>,", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Štìkeò-<PERSON><PERSON><PERSON> 194", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420606748986", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "9.7", "Total Volume": "0.028", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 24, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>1152, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Tržní1152", "city": "Strakonice", "postCode": "386 01", "phone": "+420604302441", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>1152, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1X820354886", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Tržní1152", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420604302441", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.4", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 25, "displayName": "<PERSON><PERSON><PERSON>", "personName": "<PERSON><PERSON><PERSON><PERSON>", "googleMapsLabel": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "Soň<PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "address": "V Lipkách 104, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "V Lipkách 104", "city": "STRAKONICE", "postCode": "386 01", "phone": "+420607066225", "fullGoogleMapsText": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>, <PERSON> 104, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "45T00105536", "Smìr": "8224614", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "Soò<PERSON>", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "V Lipkách 104", "Recipient Post Code": "386 01", "Recipient City": "STRAKONICE", "Recipient Phone GSM": "+420607066225", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "50", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "175"}}, {"id": 26, "displayName": "Kasá<PERSON>y", "personName": "<PERSON><PERSON>", "googleMapsLabel": "Kasárny - <PERSON><PERSON>", "companyName": "Kasá<PERSON>y", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "V Lipkách100, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "V Lipkách100", "city": "Strakonice", "postCode": "386 01", "phone": "+420732118518", "fullGoogleMapsText": "Kasárny - <PERSON><PERSON>, V Lipkách100, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1X820354926", "Smìr": "S220500", "DT": "", "Recipient Company Name": "Kasá<PERSON>y", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "V Lipkách100", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420732118518", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.03", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 27, "displayName": "CZ005CST", "personName": "<PERSON>", "googleMapsLabel": "CZ005CST - <PERSON>", "companyName": "CZ005CST", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 140, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON> 140 ", "city": "Strakonice", "postCode": "386 01", "phone": "+420773279278", "fullGoogleMapsText": "CZ005CST - <PERSON>, <PERSON><PERSON><PERSON><PERSON> 140, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09174973", "Smìr": "S220500", "DT": "", "Recipient Company Name": "CZ005CST", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Velké námìstí 140 ", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420773279278", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 28, "displayName": "Centrum-Opravy a úpravy oděvů", "personName": "", "googleMapsLabel": "Centrum-Opravy a úpravy oděvů", "companyName": "Centrum-Opravy a úpravy oděvů", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON> 212, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON> 212", "city": "Strakonice", "postCode": "386 01", "phone": "*********", "fullGoogleMapsText": "Centrum-Opravy a <PERSON> o<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 212, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "0HD00024814", "Smìr": "8224614", "DT": "", "Recipient Company Name": "Centrum-Opravy a úpravy odìvù", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Velké námìstí 212", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "*********", "Poznámky": "P_005/Pozice 5, <PERSON><PERSON> angliè<PERSON>y\nKurz angliètiny", "Poznámky zak servis": "K doruèení pøíjemci dne 14.5.2025", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "8"}}, {"id": 29, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON> 49, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Velké náměstí 49", "city": "Strakonice", "postCode": "386 01", "phone": "+420607680267", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 49, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "4MP00008069", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Velké námìstí 49", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420607680267", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 30, "displayName": "SIKO KOUPELNY a.s.", "personName": "<PERSON><PERSON>", "googleMapsLabel": "SIKO KOUPELNY a.s. - <PERSON><PERSON>", "companyName": "SIKO KOUPELNY a.s.", "recipientName": "<PERSON><PERSON>", "surname": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 195, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Volyňská 195", "city": "Strakonice", "postCode": "386 01", "phone": "*********", "fullGoogleMapsText": "SIKO KOUPELNY a.s. - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 195, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "36F00010712", "Smìr": "S220500", "DT": "", "Recipient Company Name": "SIKO KOUPELNY a.s.", "Recipient Name": "<PERSON><PERSON>", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Volyòská 195", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.8", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 31, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "personName": "<PERSON><PERSON><PERSON><PERSON>", "googleMapsLabel": "František <PERSON> - Pur<PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON><PERSON>", "recipientName": "<PERSON><PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "address": "Palacké<PERSON> náměstí 331, Strakonice / Strakonice I, 386 01", "street": "Palackého náměstí 331", "city": "Strakonice / Strakonice I", "postCode": "386 01", "phone": "00420777669228", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 331, Strakonice / Strakonice I, 386 01", "originalData": {"Order Number 1": "61Z00005918", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "Palackého námìstí 331", "Recipient Post Code": "386 01", "Recipient City": "Strakonice / Strakonice I", "Recipient Phone GSM": "00420777669228", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.7", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 32, "displayName": "ZŠ F.L. Čelakovského Strakonice", "personName": "<PERSON><PERSON><PERSON><PERSON>", "googleMapsLabel": "ZŠ F.L. Čelakovského Strakonice - Mgr<PERSON>", "companyName": "ZŠ F.L. Čelakovského Strakonice", "recipientName": "<PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1280, <PERSON><PERSON><PERSON><PERSON> 1, 386 01", "street": "Jezerní 1280", "city": "Strakonice 1", "postCode": "386 01", "phone": "+420380429353", "fullGoogleMapsText": "ZŠ F.L. <PERSON>ého Strakonice - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1280, Strak<PERSON><PERSON> 1, 386 01", "originalData": {"Order Number 1": "45T00150127", "Smìr": "S220500", "DT": "", "Recipient Company Name": "ZŠ F.L. Èelakovského Strakonice", "Recipient Name": "<PERSON>", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "Jezerní 1280", "Recipient Post Code": "386 01", "Recipient City": "Strakonice 1", "Recipient Phone GSM": "+420380429353", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "SX701501", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 33, "displayName": "DANUŠE SYNKOVÁ", "personName": "", "googleMapsLabel": "DANUŠE SYNKOVÁ", "companyName": "DANUŠE SYNKOVÁ", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON> 726, <PERSON><PERSON><PERSON><PERSON><PERSON> 1, 386 01", "street": "Profesora <PERSON> 726", "city": "STRAKONICE 1", "postCode": "386 01", "phone": "", "fullGoogleMapsText": "<PERSON>AN<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 726, <PERSON><PERSON><PERSON><PERSON><PERSON> 1, 386 01", "originalData": {"Order Number 1": "5IL00000241", "Smìr": "S220500", "DT": "", "Recipient Company Name": "DANUŠE SYNKOVÁ", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Profesora <PERSON> 726", "Recipient Post Code": "386 01", "Recipient City": "STRAKONICE 1", "Recipient Phone GSM": "", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 34, "displayName": "LUVO Praha, spol. s r.o.", "personName": "p<PERSON>", "googleMapsLabel": "LUVO Praha, spol. s r.o. - <PERSON><PERSON>", "companyName": "LUVO Praha, spol. s r.o.", "recipientName": "", "surname": "p<PERSON>", "address": "Tov<PERSON><PERSON><PERSON> 202, Strakonice II, 386 01", "street": "Tovární 202", "city": "Strakonice II", "postCode": "386 01", "phone": "+420601588577", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, spol. s r.o. <PERSON> <PERSON><PERSON>, Tov<PERSON>rn<PERSON> 202, Strakonice II, 386 01", "originalData": {"Order Number 1": "82R00012779", "Smìr": "S220500", "DT": "", "Recipient Company Name": "LUVO Praha, spol. s r.o.", "Recipient Name": "", "Recipient Surname": "p<PERSON>", "Recipient Street": "Tovární 202", "Recipient Post Code": "386 01", "Recipient City": "Strakonice II", "Recipient Phone GSM": "+420601588577", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "12", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 35, "displayName": "<PERSON>", "personName": "<PERSON><PERSON><PERSON>", "googleMapsLabel": "<PERSON> - <PERSON><PERSON><PERSON>", "companyName": "<PERSON>", "recipientName": "Jan", "surname": "<PERSON><PERSON><PERSON>", "address": "St<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Lhota 29, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Střelskohoštická Lhota 29", "city": "Střelské Hoš<PERSON>e", "postCode": "386 01", "phone": "+420773911466", "fullGoogleMapsText": "<PERSON> - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 29, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "5BI00005144", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "Jan", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Støelskohoštická Lhota 29", "Recipient Post Code": "386 01", "Recipient City": "Støelské Hoš<PERSON>e", "Recipient Phone GSM": "+420773911466", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 36, "displayName": "<PERSON>", "personName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "googleMapsLabel": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "companyName": "<PERSON>", "recipientName": "<PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 140, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 51", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON> 140", "city": "Štěkeň", "postCode": "387 51", "phone": "+420737106640", "fullGoogleMapsText": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 140, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 51", "originalData": {"Order Number 1": "49O00102102", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "<PERSON>", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON><PERSON> 140", "Recipient Post Code": "387 51", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420737106640", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "1", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 37, "displayName": "<PERSON><PERSON>", "personName": "<PERSON><PERSON><PERSON>", "googleMapsLabel": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "address": "Ra<PERSON>ilicka 410/2, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Radomilicka 410/2", "city": "<PERSON><PERSON><PERSON><PERSON>", "postCode": "389 01", "phone": "+420723085618", "fullGoogleMapsText": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 410/2, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "45976053", "Smìr": "S220602", "DT": "H-HD", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON>", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Radomilicka 410/2", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420723085618", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "2.134", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 38, "displayName": "<PERSON><PERSON>", "personName": "Reindlová", "googleMapsLabel": "Lenka  <PERSON> - Reindlová", "companyName": "<PERSON><PERSON>", "recipientName": "Len<PERSON>", "surname": "Reindlová", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>  1327, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Bavorovská  1327", "city": "Vodňany", "postCode": "389 01", "phone": "", "fullGoogleMapsText": "<PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>, Bavorovská  1327, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "04G00014888", "Smìr": "8224614", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "Len<PERSON>", "Recipient Surname": "Reindlová", "Recipient Street": "Bavorovská  1327", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "", "Poznámky": "Textil(Obleèení) - 0605-2415-1014-0568", "Poznámky zak servis": "<PERSON><PERSON><PERSON><PERSON>, opìtovné vyzvednutí 19.04.2024", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "275"}}, {"id": 39, "displayName": "<PERSON><PERSON>, spol. s r.o.", "personName": "<PERSON>", "googleMapsLabel": "<PERSON><PERSON>, spol. s r.o. - <PERSON>", "companyName": "<PERSON><PERSON>, spol. s r.o.", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>1284, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Číčenická1284", "city": "Vodňany", "postCode": "389 01", "phone": "+420606075135", "fullGoogleMapsText": "<PERSON><PERSON>, spol. s r.o. - <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>1284, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1X820354992", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON>, spol. s r.o.", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Èíèenická1284", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420606075135", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.13", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 40, "displayName": "AlzaBox Dr. <PERSON>", "personName": "<PERSON><PERSON>", "googleMapsLabel": "AlzaBox Dr. <PERSON> - <PERSON><PERSON>", "companyName": "AlzaBox Dr. <PERSON>", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "<PERSON><PERSON> 1285, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON>. <PERSON> 1285", "city": "Vodňany", "postCode": "389 01", "phone": "+420737377495", "fullGoogleMapsText": "AlzaBox Dr. <PERSON><PERSON> <PERSON> <PERSON><PERSON>, Dr. <PERSON><PERSON> 1285, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09153415", "Smìr": "S220602", "DT": "", "Recipient Company Name": "AlzaBox Dr. <PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "<PERSON>. <PERSON> 1285", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420737377495", "Poznámky": "", "Poznámky zak servis": "K doruèení pøíjemci dne 22.5.2025", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "2"}}, {"id": 41, "displayName": "Elektro <PERSON>", "personName": "", "googleMapsLabel": "Elektro <PERSON>", "companyName": "Elektro <PERSON>", "recipientName": "", "surname": "", "address": "Kalinovo nám. 40, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Kalinovo nám. 40", "city": "Vodňany", "postCode": "389 01", "phone": "+420606641890", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, Kalinovo nám. 40, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "46Y00003012", "Smìr": "S220602", "DT": "", "Recipient Company Name": "Elektro <PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Kalinovo nám. 40", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420606641890", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 42, "displayName": "WCOMP s.r.o.", "personName": "", "googleMapsLabel": "WCOMP s.r.o.", "companyName": "WCOMP s.r.o.", "recipientName": "", "surname": "", "address": "nám<PERSON> <PERSON><PERSON><PERSON> 194, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "nám. Svobody 194", "city": "Vodňany", "postCode": "389 01", "phone": "00420774280118", "fullGoogleMapsText": "WCOMP s.r.o., n<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> 194, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "77B03601253", "Smìr": "S220602", "DT": "", "Recipient Company Name": "WCOMP s.r.o.", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "nám. Svobody 194", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "00420774280118", "Poznámky": "Tel. avizo: 00420774280118", "Poznámky zak servis": "", "Total Weight": "6.3", "Total Volume": "0.036", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 43, "displayName": "<PERSON>ček", "personName": "", "googleMapsLabel": "<PERSON>ček", "companyName": "<PERSON>ček", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 1214, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Stožická 1214", "city": "Vodňany", "postCode": "389 01", "phone": "+420602208875", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 1214, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "90T00051792", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Stožická 1214", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420602208875", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "6.6", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 44, "displayName": "<PERSON><PERSON>", "personName": "Šenarová", "googleMapsLabel": "<PERSON><PERSON> - Š<PERSON>rov<PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "<PERSON><PERSON>", "surname": "Šenarová", "address": "Ohrazenice 5, <PERSON><PERSON><PERSON>, 387 16", "street": "Ohrazenice 5", "city": "Volenice", "postCode": "387 16", "phone": "+420603703129", "fullGoogleMapsText": "<PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>, Ohrazenice 5, <PERSON><PERSON><PERSON>, 387 16", "originalData": {"Order Number 1": "45T00150007", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON>", "Recipient Surname": "Šenarová", "Recipient Street": "Ohrazenice 5", "Recipient Post Code": "387 16", "Recipient City": "Volenice", "Recipient Phone GSM": "+420603703129", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "10", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S880100", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 45, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "<PERSON>", "surname": "<PERSON>", "address": "Volenice 124, <PERSON><PERSON><PERSON>, 387 16", "street": "Volenice 124", "city": "Volenice", "postCode": "387 16", "phone": "*********", "fullGoogleMapsText": "<PERSON>, Volenice 124, <PERSON><PERSON><PERSON>, 387 16", "originalData": {"Order Number 1": "30O00025027", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "<PERSON>", "Recipient Surname": "<PERSON>", "Recipient Street": "Volenice 124", "Recipient Post Code": "387 16", "Recipient City": "Volenice", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "Nový termín doruèení dne 26.05.2025", "Total Weight": "20.4", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "1"}}, {"id": 46, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "Cehnice 168, <PERSON><PERSON><PERSON>, 387 52", "street": "Cehnice 168", "city": "Cehnice", "postCode": "387 52", "phone": "+420725569449", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON> 168, <PERSON><PERSON><PERSON>, 387 52", "originalData": {"Order Number 1": "1AM08884974", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Cehnice 168", "Recipient Post Code": "387 52", "Recipient City": "Cehnice", "Recipient Phone GSM": "+420725569449", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.61", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 47, "displayName": "Agro Čejetice s.r.o.", "personName": "<PERSON><PERSON><PERSON>", "googleMapsLabel": "Agro Čejetice s.r.o. - <PERSON><PERSON><PERSON>", "companyName": "Agro Čejetice s.r.o.", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "Čejetice 106, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Čejetice 106", "city": "cejetice", "postCode": "386 01", "phone": "+420725795185", "fullGoogleMapsText": "Agro Čejetice s.r.o. - <PERSON><PERSON><PERSON>, Čej<PERSON><PERSON> 106, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM08791591", "Smìr": "S220500", "DT": "", "Recipient Company Name": "Agro Èejetice s.r.o.", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Èejetice 106", "Recipient Post Code": "386 01", "Recipient City": "cejetice", "Recipient Phone GSM": "+420725795185", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "2", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 48, "displayName": "petr sterbik", "personName": "PETR ŠTERBIK", "googleMapsLabel": "petr sterbik - PETR ŠTERBIK", "companyName": "petr sterbik", "recipientName": "", "surname": "PETR ŠTERBIK", "address": "ČEJETICE 166, ČEJETICE 166, 386 01", "street": "ČEJETICE 166", "city": "ČEJETICE 166", "postCode": "386 01", "phone": "+420777206722", "fullGoogleMapsText": "<PERSON>r ster<PERSON>k - <PERSON><PERSON><PERSON> ŠTERBIK, ČEJETICE 166, ČEJETICE 166, 386 01", "originalData": {"Order Number 1": "1AM09033009", "Smìr": "S220500", "DT": "", "Recipient Company Name": "petr sterbik", "Recipient Name": "", "Recipient Surname": "PETR ŠTERBIK", "Recipient Street": "ÈEJETICE 166", "Recipient Post Code": "386 01", "Recipient City": "ÈEJETICE 166", "Recipient Phone GSM": "+420777206722", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 49, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 57, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Drahonice 57", "city": "Drahonice", "postCode": "389 01", "phone": "+420734635056", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 57, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM08923232", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Drahonice 57", "Recipient Post Code": "389 01", "Recipient City": "Drahonice", "Recipient Phone GSM": "+420734635056", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1.5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 50, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "28. <PERSON><PERSON><PERSON><PERSON> 85, <PERSON><PERSON><PERSON>, 387 11", "street": "28. <PERSON><PERSON><PERSON><PERSON> 85", "city": "Katovice", "postCode": "387 11", "phone": "+420777278588", "fullGoogleMapsText": "<PERSON>, 28. <PERSON><PERSON><PERSON><PERSON> 85, <PERSON><PERSON><PERSON>, 387 11", "originalData": {"Order Number 1": "1AM09202905", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "28. <PERSON><PERSON><PERSON><PERSON> 85", "Recipient Post Code": "387 11", "Recipient City": "Katovice", "Recipient Phone GSM": "+420777278588", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "18", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 51, "displayName": "<PERSON><PERSON><PERSON>,", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>,", "companyName": "<PERSON><PERSON><PERSON>,", "recipientName": "", "surname": "", "address": "Husovo naměsti 10, <PERSON><PERSON><PERSON>, 387 11", "street": "Husovo naměsti 10", "city": "Katovice", "postCode": "387 11", "phone": "+420777223770", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>,, <PERSON><PERSON><PERSON> 10, <PERSON><PERSON><PERSON>, 387 11", "originalData": {"Order Number 1": "24X00879607", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>,", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Husovo namìsti 10", "Recipient Post Code": "387 11", "Recipient City": "Katovice", "Recipient Phone GSM": "+420777223770", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "24.36", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 52, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 137, <PERSON><PERSON><PERSON>, 387 11", "street": "Nádražní 137", "city": "Katovice", "postCode": "387 11", "phone": "*********", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 137, <PERSON><PERSON><PERSON>, 387 11", "originalData": {"Order Number 1": "24X01024272", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Nádražní 137", "Recipient Post Code": "387 11", "Recipient City": "Katovice", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "14", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 53, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON> 70, <PERSON><PERSON><PERSON>, 387 16", "street": "<PERSON><PERSON><PERSON> 70", "city": "<PERSON><PERSON><PERSON>", "postCode": "387 16", "phone": "+420721441435", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> 70, <PERSON><PERSON><PERSON>, 387 16", "originalData": {"Order Number 1": "1AM09223696", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON> 70", "Recipient Post Code": "387 16", "Recipient City": "<PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420721441435", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "9", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 54, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 83, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 72", "street": "Libějovice 83", "city": "Libějovice", "postCode": "387 72", "phone": "+420721487339", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 83, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 72", "originalData": {"Order Number 1": "1AM09221079", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "Libìjovice 83", "Recipient Post Code": "387 72", "Recipient City": "Libìjovice", "Recipient Phone GSM": "+420721487339", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.72", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 55, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 29, <PERSON><PERSON><PERSON><PERSON>, 387 36", "street": "Me<PERSON><PERSON>ov 29", "city": "<PERSON><PERSON><PERSON><PERSON>", "postCode": "387 36", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 29, <PERSON><PERSON><PERSON><PERSON>, 387 36", "originalData": {"Order Number 1": "54D00035844", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON><PERSON>", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON> 29", "Recipient Post Code": "387 36", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1.5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 56, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "", "address": "Zadní Ptákovice 20, <PERSON><PERSON><PERSON>ehovice, 386 01", "street": "Zadní Ptákovice 20", "city": "Nebřehovice", "postCode": "386 01", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 20, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "24X01012291", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Zadní Ptákovice 20", "Recipient Post Code": "386 01", "Recipient City": "Nebøehovice", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "4.88", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 57, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "<PERSON><PERSON>", "surname": "<PERSON><PERSON>", "address": "Přestovice 5, <PERSON><PERSON><PERSON><PERSON>�<PERSON><PERSON>, 386 01", "street": "Přestovice 5", "city": "Přeš�ovice", "postCode": "386 01", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 5, <PERSON><PERSON><PERSON><PERSON>�<PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "2KX00039094", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON>", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Pøestovice 5", "Recipient Post Code": "386 01", "Recipient City": "Pøeš�ovice", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.8", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 58, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON> Rutaku 335, <PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Do Rutaku 335", "city": "Radomysl", "postCode": "387 31", "phone": "+420603821959", "fullGoogleMapsText": "<PERSON>, <PERSON> 335, <PERSON><PERSON><PERSON><PERSON>, 387 31", "originalData": {"Order Number 1": "1AM09214366", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Do Rutaku 335", "Recipient Post Code": "387 31", "Recipient City": "Radomysl", "Recipient Phone GSM": "+420603821959", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "3", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 59, "displayName": "<PERSON><PERSON>,", "personName": "", "googleMapsLabel": "<PERSON><PERSON>,", "companyName": "<PERSON><PERSON>,", "recipientName": "", "surname": "", "address": "Na Travnikach 225, <PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Na Travnikach 225", "city": "Radomysl", "postCode": "387 31", "phone": "+420602115750", "fullGoogleMapsText": "<PERSON><PERSON>,, <PERSON> 225, <PERSON><PERSON><PERSON><PERSON>, 387 31", "originalData": {"Order Number 1": "4RA00064576", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>,", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Na Travnikach 225", "Recipient Post Code": "387 31", "Recipient City": "Radomysl", "Recipient Phone GSM": "+420602115750", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 60, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "Blatenská 62  CZ1, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Blatenská 62  CZ1", "city": "Radomyšl", "postCode": "387 31", "phone": "+420607253585", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 62  C<PERSON>1, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "originalData": {"Order Number 1": "1AM08860754", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Blatenská 62  CZ1", "Recipient Post Code": "387 31", "Recipient City": "Radomyšl", "Recipient Phone GSM": "+420607253585", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 61, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON> 41, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON> 41", "city": "Radomyšl", "postCode": "386 01", "phone": "+420721430219", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 41, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM08925089", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON> 41", "Recipient Post Code": "386 01", "Recipient City": "Radomyšl", "Recipient Phone GSM": "+420721430219", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "4", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 62, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "Maltézské náměstí 7, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Maltézské náměstí 7", "city": "Radomyšl", "postCode": "387 31", "phone": "+420724277211", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, Mal<PERSON>z<PERSON>é náměstí 7, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "originalData": {"Order Number 1": "1AM09135720", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Maltézské námìstí 7", "Recipient Post Code": "387 31", "Recipient City": "Radomyšl", "Recipient Phone GSM": "+420724277211", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "9", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 63, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "Na Trávník<PERSON> 225, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Na Trávníkách 225", "city": "Radomyšl", "postCode": "387 31", "phone": "+420602115750", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON> 225, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "originalData": {"Order Number 1": "1AM09042222", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Na Trávníkách 225", "Recipient Post Code": "387 31", "Recipient City": "Radomyšl", "Recipient Phone GSM": "+420602115750", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 64, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 172, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Sídl<PERSON>š<PERSON>ě 172", "city": "Radomyšl", "postCode": "387 31", "phone": "+420607836963", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 172, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "originalData": {"Order Number 1": "3SU00005938", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Sídlištì 172", "Recipient Post Code": "387 31", "Recipient City": "Radomyšl", "Recipient Phone GSM": "+420607836963", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "8", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 65, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "Za Humny 1, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "street": "Za Humny 1", "city": "Radomyšl", "postCode": "387 31", "phone": "+420602786135", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON> 1, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 31", "originalData": {"Order Number 1": "1AM09220588", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Za Humny 1", "Recipient Post Code": "387 31", "Recipient City": "Radomyšl", "Recipient Phone GSM": "+420602786135", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 66, "displayName": "<PERSON>", "personName": "Zdychynec", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "Zdychynec", "address": "Radošovice 10, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Radošovice 10", "city": "Radošovice", "postCode": "386 01", "phone": "+420725855848", "fullGoogleMapsText": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 10, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "9S500262081", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "Zdychynec", "Recipient Street": "Radošovice 10", "Recipient Post Code": "386 01", "Recipient City": "Radošovice", "Recipient Phone GSM": "+420725855848", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1.8", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 67, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>108, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Radošovice108", "city": "Radošovice", "postCode": "386 01", "phone": "+420602814551", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>108, <PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1X820351634", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Radošovice108", "Recipient Post Code": "386 01", "Recipient City": "Radošovice", "Recipient Phone GSM": "+420602814551", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.03", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 68, "displayName": "<PERSON>", "personName": "<PERSON>", "googleMapsLabel": "Giles - <PERSON>", "companyName": "<PERSON>", "recipientName": "<PERSON>", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON> 247, <PERSON><PERSON><PERSON>, 387 51", "street": "Slatinska 247", "city": "<PERSON><PERSON><PERSON>", "postCode": "387 51", "phone": "+420702995194", "fullGoogleMapsText": "Giles - <PERSON>, <PERSON><PERSON><PERSON><PERSON> 247, <PERSON><PERSON><PERSON>, 387 51", "originalData": {"Order Number 1": "5310006678651", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "<PERSON>", "Recipient Surname": "", "Recipient Street": "Slatinska 247", "Recipient Post Code": "387 51", "Recipient City": "<PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420702995194", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 69, "displayName": "Domácí péče ČČK - obecně prospěšná společnost", "personName": "<PERSON><PERSON><PERSON><PERSON>", "googleMapsLabel": "Domácí péče ČČK - obecně prospěšná společnost - Mgr. <PERSON><PERSON><PERSON><PERSON>", "companyName": "Domácí péče ČČK - obecně prospěšná společnost", "recipientName": "<PERSON><PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON> 1118, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON> 1118", "city": "Strakonice", "postCode": "386 01", "phone": "*********", "fullGoogleMapsText": "Domá<PERSON>í péče ČČK - obecně prospěšná společnost - Mgr<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> 1118, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "14G00012202", "Smìr": "S220500", "DT": "", "Recipient Company Name": "Domácí péèe <PERSON>K - obecnì prospìšná spoleènost", "Recipient Name": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON> 1118", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.8", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 70, "displayName": "Mir<PERSON>", "personName": "", "googleMapsLabel": "Mir<PERSON>", "companyName": "Mir<PERSON>", "recipientName": "", "surname": "Mir<PERSON>", "address": "Baarova268 38601, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Baarova268 38601", "city": "Strakonice", "postCode": "386 01", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON>, Baarova268 38601, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09209573", "Smìr": "S220500", "DT": "", "Recipient Company Name": "Mir<PERSON>", "Recipient Name": "", "Recipient Surname": "Mir<PERSON>", "Recipient Street": "Baarova268 38601", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "25", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 71, "displayName": "OLYMPIA PAPÍR s.r.o", "personName": "", "googleMapsLabel": "OLYMPIA PAPÍR s.r.o", "companyName": "OLYMPIA PAPÍR s.r.o", "recipientName": "", "surname": "", "address": "Bezděkov<PERSON><PERSON> 30, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Bezděkovská 30", "city": "Strakonice", "postCode": "386 01", "phone": "+420728862596", "fullGoogleMapsText": "OLYMPIA PAPÍR s.r.o, Bezděkovská 30, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "24X00880846", "Smìr": "S220500", "DT": "", "Recipient Company Name": "OLYMPIA PAPÍR s.r.o", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Bezdìkovská 30", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420728862596", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 72, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "Čejetice 136, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Čejetice 136", "city": "Strakonice", "postCode": "386 01", "phone": "+420777256415", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON> 136, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09145453", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Èejetice 136", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420777256415", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 73, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "", "address": "Čejetice 70, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Čejetice 70", "city": "Strakonice", "postCode": "386 01", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 70, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "24X01024607", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Èejetice 70", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "14", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 74, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 1124, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Čelakovskeho 1124", "city": "Strakonice", "postCode": "386 01", "phone": "+420775102389", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 1124, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "99N00257698", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Èelakovskeho 1124", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420775102389", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.86", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 75, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 509, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Českých lesů 509", "city": "Strakonice", "postCode": "386 01", "phone": "+420603868037", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 509, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09215782", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON><PERSON> 509", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420603868037", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "2.35", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 76, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON><PERSON><PERSON> 31, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Domanice 31", "city": "Strakonice", "postCode": "386 01", "phone": "+420602205134", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON> 31, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09219349", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Domanice 31", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420602205134", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 77, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1289, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1289", "city": "Strakonice", "postCode": "386 01", "phone": "+420721717180", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1289, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM08375681", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1289", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420721717180", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 78, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON> 321, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON> 321", "city": "Strakonice", "postCode": "386 01", "phone": "+420776173758", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON> 321, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "99N00257392", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "<PERSON><PERSON><PERSON> 321", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420776173758", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.19", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 79, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 124, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Hranični 124", "city": "Strakonice", "postCode": "386 01", "phone": "+420776338592", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 124, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09202131", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Hranièni 124", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420776338592", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "10", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 80, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "address": "Chmelenského 167, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Chmelenského 167", "city": "Strakonice", "postCode": "386 01", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 167, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "54D00039940", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON><PERSON>", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Chmelenského 167", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "9", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 81, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON><PERSON>", "recipientName": "<PERSON><PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 293, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON>va 293", "city": "Strakonice", "postCode": "386 01", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 293, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "2KX00039096", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON>va 293", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "4", "Total Volume": "0", "Box count": "4", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 82, "displayName": "OTAVANET, s.r.o.", "personName": "", "googleMapsLabel": "OTAVANET, s.r.o.", "companyName": "OTAVANET, s.r.o.", "recipientName": "OTAVANET, s.r.o.", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON> 175, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON> 175", "city": "Strakonice", "postCode": "386 01", "phone": "+420777321402", "fullGoogleMapsText": "OTAVANE<PERSON>, s.r.o., <PERSON><PERSON><PERSON><PERSON> 175, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "0GR00028090", "Smìr": "S220500", "DT": "", "Recipient Company Name": "OTAVANET, s.r.o.", "Recipient Name": "OTAVANET, s.r.o.", "Recipient Surname": "", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON> 175", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420777321402", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "30", "Total Volume": "0", "Box count": "2", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 83, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "Krty-Hrade<PERSON> 11, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Krty-Hradec 11", "city": "Strakonice", "postCode": "386 01", "phone": "+420776424366", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 11, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM08948911", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Krty-Hradec 11", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420776424366", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "22", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 84, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 11, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Mladějovice 11", "city": "Strakonice", "postCode": "386 01", "phone": "+420731036580", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 11, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM08538200", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Mladìjovice 11", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420731036580", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "10", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 85, "displayName": "<PERSON><PERSON><PERSON>", "personName": "Kroup<PERSON>", "googleMapsLabel": "<PERSON><PERSON><PERSON> - K<PERSON>a", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "Oldřich", "surname": "Kroup<PERSON>", "address": "<PERSON><PERSON><PERSON> 105, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON> 105", "city": "Strakonice", "postCode": "386 01", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 105, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1Z700586243", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON><PERSON>", "Recipient Surname": "Kroup<PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON> 105", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "*********", "Poznámky": "Textil(Obleèení) - 7795195", "Poznámky zak servis": "", "Total Weight": "1.45", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 86, "displayName": "Zdeňka Kocová", "personName": "", "googleMapsLabel": "Zdeňka Kocová", "companyName": "Zdeňka Kocová", "recipientName": "", "surname": "Zdeňka Kocová", "address": "<PERSON><PERSON><PERSON><PERSON> 1109, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON>ého 1109", "city": "Strakonice", "postCode": "386 01", "phone": "+420723088687", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1109, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM08724721", "Smìr": "S220500", "DT": "", "Recipient Company Name": "Zdeòka <PERSON>", "Recipient Name": "", "Recipient Surname": "Zdeòka <PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON>ého 1109", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420723088687", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "2", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 87, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "Na Muškách 625, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Na Muškách 625", "city": "Strakonice", "postCode": "386 01", "phone": "+420602321574", "fullGoogleMapsText": "<PERSON>, <PERSON> 625, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09221124", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Na Muškách 625", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420602321574", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "6", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 88, "displayName": "OX BOX Nádražní", "personName": "<PERSON>", "googleMapsLabel": "OX BOX Nádražní - <PERSON>", "companyName": "OX BOX Nádražní", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 337, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Nádražní 337", "city": "Strakonice", "postCode": "386 01", "phone": "+420776882842", "fullGoogleMapsText": "OX BOX Nádražní - <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 337, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09160217", "Smìr": "S220500", "DT": "", "Recipient Company Name": "OX BOX Nádražní", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Nádražní 337", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420776882842", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "2", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 89, "displayName": "AutomaCZ", "personName": "<PERSON><PERSON>", "googleMapsLabel": "AutomaCZ - Vit Boska", "companyName": "AutomaCZ", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "Nebřehovice 39, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Nebřehovice 39", "city": "Strakonice", "postCode": "386 01", "phone": "+420602707551", "fullGoogleMapsText": "AutomaCZ - <PERSON><PERSON>, Nebřehovice 39, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09212843", "Smìr": "S220500", "DT": "", "Recipient Company Name": "AutomaCZ", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Nebøehovice 39", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420602707551", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 90, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON> 632, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON> 632", "city": "Strakonice", "postCode": "386 01", "phone": "+420728339279", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> 632, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09179276", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON> 632", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420728339279", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "8", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 91, "displayName": "schambeck bohemia s.r.o.", "personName": "<PERSON>", "googleMapsLabel": "scha<PERSON><PERSON> bohemia s.r.o. - <PERSON>", "companyName": "schambeck bohemia s.r.o.", "recipientName": "<PERSON>", "surname": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 893, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Písecká 893", "city": "Strakonice", "postCode": "386 01", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON> bohemia s.r.o. - <PERSON>, <PERSON><PERSON><PERSON><PERSON> 893, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "14G00008797", "Smìr": "S220500", "DT": "", "Recipient Company Name": "schambeck bohemia s.r.o.", "Recipient Name": "<PERSON>", "Recipient Surname": "<PERSON>", "Recipient Street": "Písecká 893", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.8", "Total Volume": "0", "Box count": "2", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 92, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "Pracejovice 14, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Pracejovice 14", "city": "Strakonice", "postCode": "386 01", "phone": "+420730871442", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 14, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09210481", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Pracejovice 14", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420730871442", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 93, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "Přes�ovice 48, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Přes�ovice 48", "city": "Strakonice", "postCode": "386 01", "phone": "+420735199019", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON>�<PERSON><PERSON> 48, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09202401", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Pøes�ovice 48", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420735199019", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "14", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 94, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 430, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON> 430", "city": "Strakonice", "postCode": "386 01", "phone": "+420605255734", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 430, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09208593", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON> 430", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420605255734", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "20", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 95, "displayName": "Nemocnice Strakonice", "personName": "<PERSON>", "googleMapsLabel": "Nemocnice Strakonice - <PERSON>", "companyName": "Nemocnice Strakonice", "recipientName": "", "surname": "<PERSON>", "address": "Radomyš<PERSON><PERSON><PERSON> 336, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Radomyšlská 336", "city": "Strakonice", "postCode": "386 01", "phone": "+420723738999", "fullGoogleMapsText": "Nemocnice Strakonice - <PERSON>, Ra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 336, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09221775", "Smìr": "S220500", "DT": "", "Recipient Company Name": "Nemocnice Strakonice", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Radomyšlská 336", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420723738999", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "2", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 96, "displayName": "Libor <PERSON>", "personName": "", "googleMapsLabel": "Libor <PERSON>", "companyName": "Libor <PERSON>", "recipientName": "", "surname": "Libor <PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1. <PERSON><PERSON><PERSON> 1141, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Sídliště 1. máje 1141", "city": "Strakonice", "postCode": "386 01", "phone": "+420721141548", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1. <PERSON><PERSON><PERSON> 1141, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM08333020", "Smìr": "S220500", "DT": "", "Recipient Company Name": "Libor <PERSON>", "Recipient Name": "", "Recipient Surname": "Libor <PERSON>", "Recipient Street": "Sídlištì 1. máje 1141", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420721141548", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 97, "displayName": "<PERSON><PERSON><PERSON>", "personName": "JIŘI BENES", "googleMapsLabel": "<PERSON><PERSON><PERSON> - JIŘI BENES", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "JIŘI BENES", "address": "Sousedovice 37, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Sousedovice 37", "city": "Strakonice", "postCode": "386 01", "phone": "+420777321401", "fullGoogleMapsText": "<PERSON><PERSON><PERSON> - JIŘI <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 37, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09191024", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "JIØI BENES", "Recipient Street": "Sousedovice 37", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420777321401", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "3.6", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 98, "displayName": "MBM Westra s.r.o.", "personName": "<PERSON><PERSON><PERSON>", "googleMapsLabel": "MBM Westra s.r.o. - <PERSON><PERSON><PERSON>", "companyName": "MBM Westra s.r.o.", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "Sousedovice 61 61 / 61, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Sousedovice 61 61 / 61", "city": "Strakonice", "postCode": "386 01", "phone": "+420723330946", "fullGoogleMapsText": "MBM Westra s.r.o. - <PERSON><PERSON><PERSON>, Sousedovice 61 61 / 61, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09215280", "Smìr": "S220500", "DT": "", "Recipient Company Name": "MBM Westra s.r.o.", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Sousedovice 61 61 / 61", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420723330946", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 99, "displayName": "Zdeněk <PERSON>ek", "personName": "", "googleMapsLabel": "Zdeněk <PERSON>ek", "companyName": "Zdeněk <PERSON>ek", "recipientName": "", "surname": "Zdeněk <PERSON>ek", "address": "<PERSON><PERSON><PERSON><PERSON>, 81, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON>usedov<PERSON>, 81", "city": "Strakonice", "postCode": "386 01", "phone": "+420724589997", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 81, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM08388897", "Smìr": "S220500", "DT": "", "Recipient Company Name": "Zdenìk <PERSON>", "Recipient Name": "", "Recipient Surname": "Zdenìk <PERSON>", "Recipient Street": "<PERSON>usedov<PERSON>, 81", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420724589997", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "7", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 100, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 209, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Stavbařů 209", "city": "Strakonice", "postCode": "386 01", "phone": "+420777572001", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 209, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM08289034", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Stavbaøù 209", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420777572001", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "6", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 101, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 358, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Šumavská 358", "city": "Strakonice", "postCode": "386 01", "phone": "+420776021374", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 358, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "35O00427365", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Šumavská 358", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420776021374", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 102, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON><PERSON><PERSON> 1152, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Trzni 1152", "city": "Strakonice", "postCode": "386 01", "phone": "+420792336415", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON> 1152, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09199895", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Trzni 1152", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420792336415", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.35", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 103, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON> <PERSON> 793, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "V Raji 793", "city": "Strakonice", "postCode": "386 01", "phone": "+420725502815", "fullGoogleMapsText": "<PERSON>, <PERSON> 793, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM08441868", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "V Raji 793", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420725502815", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 104, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "<PERSON>", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON> 460, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Vaclavska 460 ", "city": "Strakonice", "postCode": "386 01", "phone": "1", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON> 460, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "7366287754982", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "<PERSON>", "Recipient Surname": "", "Recipient Street": "Vaclavska 460 ", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "1", "Poznámky": "in case of undelivery RETURN", "Poznámky zak servis": "", "Total Weight": "0.05", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 105, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON>el<PERSON> 9, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Velka Turna 9", "city": "Strakonice", "postCode": "386 01", "phone": "+420606624696", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 9, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09194839", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Velka Turna 9", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420606624696", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 106, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON> 425, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON>kova 425", "city": "Strakonice", "postCode": "386 01", "phone": "+420722042908", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 425, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM08445737", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON>kova 425", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420722042908", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "11", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 107, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "Bezděkovska 30 (optika axis) 30, Strakonice - Strakonice I, 386 01", "street": "Bezděkovska 30 (optika axis) 30", "city": "Strakonice - Strakonice I", "postCode": "386 01", "phone": "+420723108401", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 30 (optika axis) 30, Strakonice - Strakonice I, 386 01", "originalData": {"Order Number 1": "1AM09224190", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Bezdìkovska 30 (optika axis) 30", "Recipient Post Code": "386 01", "Recipient City": "Strakonice - Strakonice I", "Recipient Phone GSM": "+420723108401", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 108, "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Betty", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Betty", "companyName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Betty", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 105, <PERSON><PERSON><PERSON><PERSON> (okres Strakonice), 386 01", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 105", "city": "Strakonice (okres Strakonice)", "postCode": "386 01", "phone": "+*********509", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, 105, <PERSON><PERSON><PERSON><PERSON> (okres Strakonice), 386 01", "originalData": {"Order Number 1": "47I00548005", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Betty", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 105", "Recipient Post Code": "386 01", "Recipient City": "Strakonice (okres Strakonice)", "Recipient Phone GSM": "+*********509", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 109, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON>, 187, <PERSON><PERSON><PERSON><PERSON> (okres Strakonice), 386 01", "street": "<PERSON><PERSON><PERSON><PERSON>, 187", "city": "Strakonice (okres Strakonice)", "postCode": "386 01", "phone": "+420721990408", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 187, <PERSON><PERSON><PERSON><PERSON> (okres Strakonice), 386 01", "originalData": {"Order Number 1": "47I00548344", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON>, 187", "Recipient Post Code": "386 01", "Recipient City": "Strakonice (okres Strakonice)", "Recipient Phone GSM": "+420721990408", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 110, "displayName": "Lubos Potuznik", "personName": "", "googleMapsLabel": "Lubos Potuznik", "companyName": "Lubos Potuznik", "recipientName": "", "surname": "Lubos Potuznik", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1158, <PERSON><PERSON><PERSON><PERSON> 1, 386 01", "street": "Chelčického 1158", "city": "Strakonice 1", "postCode": "386 01", "phone": "+420722926841", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1158, <PERSON><PERSON><PERSON><PERSON> 1, 386 01", "originalData": {"Order Number 1": "1AM08932793", "Smìr": "S220500", "DT": "", "Recipient Company Name": "Lubos Potuznik", "Recipient Name": "", "Recipient Surname": "Lubos Potuznik", "Recipient Street": "Chelèickeho 1158", "Recipient Post Code": "386 01", "Recipient City": "Strakonice 1", "Recipient Phone GSM": "+420722926841", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "2.45", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 111, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "Radomylsk  336, Strakonice 1, 386 01", "street": "Radomylsk  336", "city": "Strakonice 1", "postCode": "386 01", "phone": "+420606306683", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>  336, <PERSON><PERSON><PERSON><PERSON> 1, 386 01", "originalData": {"Order Number 1": "1AM06382030", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Radomylsk  336", "Recipient Post Code": "386 01", "Recipient City": "Strakonice 1", "Recipient Phone GSM": "+420606306683", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "9.78", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "110"}}, {"id": 112, "displayName": "RENATA KOUBOVÁ", "personName": "", "googleMapsLabel": "RENATA KOUBOVÁ", "companyName": "RENATA KOUBOVÁ", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 192, <PERSON><PERSON><PERSON><PERSON> 1, 386 01", "street": "Volyňská 192", "city": "Strakonice 1", "postCode": "386 01", "phone": "", "fullGoogleMapsText": "RENATA KOUBOVÁ, Volyňská 192, Strakonice 1, 386 01", "originalData": {"Order Number 1": "5HU00000032", "Smìr": "S220500", "DT": "", "Recipient Company Name": "RENATA KOUBOVÁ", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Volyòská 192", "Recipient Post Code": "386 01", "Recipient City": "Strakonice 1", "Recipient Phone GSM": "", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "2.94", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 113, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "", "address": "Baarova 1062, Strakonice Strakonice I, 386 01", "street": "Baarova 1062", "city": "Strakonice Strakonice I", "postCode": "386 01", "phone": "00420724182264", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON> 1062, Strakonice Strakonice I, 386 01", "originalData": {"Order Number 1": "77B03565419", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Baarova 1062", "Recipient Post Code": "386 01", "Recipient City": "Strakonice Strakonice I", "Recipient Phone GSM": "00420724182264", "Poznámky": "Tel. avizo: 00420724182264", "Poznámky zak servis": "", "Total Weight": "1.07", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 114, "displayName": "Basedesign, <PERSON>", "personName": "", "googleMapsLabel": "Basedesign, <PERSON>", "companyName": "Basedesign, <PERSON>", "recipientName": "", "surname": "", "address": "Lidick<PERSON> 300, Strakonice Strakonice I, 386 01", "street": "Lidická 300", "city": "Strakonice Strakonice I", "postCode": "386 01", "phone": "00420602247218", "fullGoogleMapsText": "Basedesign, <PERSON>, <PERSON><PERSON><PERSON> 300, Strakonice Strakonice I, 386 01", "originalData": {"Order Number 1": "77B03601684", "Smìr": "S220500", "DT": "", "Recipient Company Name": "Basedesign, <PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Lidická 300", "Recipient Post Code": "386 01", "Recipient City": "Strakonice Strakonice I", "Recipient Phone GSM": "00420602247218", "Poznámky": "Tel. avizo: 00420602247218", "Poznámky zak servis": "", "Total Weight": "10.7", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 115, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON><PERSON>", "recipientName": "", "surname": "", "address": "Strašice 14, <PERSON><PERSON><PERSON><PERSON>, 387 16", "street": "Strašice 14", "city": "Strašice", "postCode": "387 16", "phone": "+420721787469", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON> Dana, Strašice 14, <PERSON><PERSON><PERSON><PERSON>, 387 16", "originalData": {"Order Number 1": "74M00242241", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Strašice 14", "Recipient Post Code": "387 16", "Recipient City": "Strašice", "Recipient Phone GSM": "+420721787469", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.53", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 116, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "<PERSON><PERSON>", "surname": "", "address": "<PERSON><PERSON><PERSON>  28, <PERSON>rel<PERSON><PERSON>, 387 15", "street": "Kozlov  28 ", "city": "Strelské Hostice", "postCode": "387 15", "phone": "+*********** 97", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>  28, <PERSON><PERSON><PERSON><PERSON>, 387 15", "originalData": {"Order Number 1": "7366287063119", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON>", "Recipient Surname": "", "Recipient Street": "Kozlov  28 ", "Recipient Post Code": "387 15", "Recipient City": "Strelské Hostice", "Recipient Phone GSM": "+*********** 97", "Poznámky": "in case of undelivery RETURN", "Poznámky zak servis": "", "Total Weight": "0.14", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 117, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 148, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 15", "street": "Střelské <PERSON> 148", "city": "Střelské Hoš<PERSON>e", "postCode": "387 15", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 148, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 15", "originalData": {"Order Number 1": "24X01011462", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Støelské <PERSON>e 148", "Recipient Post Code": "387 15", "Recipient City": "Støelské Hoš<PERSON>e", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "10.198", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 118, "displayName": "<PERSON><PERSON><PERSON>,", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>,", "companyName": "<PERSON><PERSON><PERSON>,", "recipientName": "", "surname": "", "address": "Sedl<PERSON> 22, <PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>, 386 01", "street": "Sedlo 22", "city": "Střelské Hoštice - Sedlo", "postCode": "386 01", "phone": "+420608239687", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>,, <PERSON><PERSON><PERSON> 22, <PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "24X00856514", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>,", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Sedlo 22", "Recipient Post Code": "386 01", "Recipient City": "Støelské Hoštice - Sedlo", "Recipient Phone GSM": "+420608239687", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 119, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "Třebohostice 12, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 387 37", "street": "Třebohostice 12", "city": "Třebohostice", "postCode": "387 37", "phone": "+420730625641", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 12, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 387 37", "originalData": {"Order Number 1": "1AM09224643", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Tøebohostice 12", "Recipient Post Code": "387 37", "Recipient City": "Tøebohostice", "Recipient Phone GSM": "+420730625641", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 120, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON><PERSON>", "address": "Třebohostice 45, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 387 37", "street": "Třebohostice 45", "city": "Třebohostice", "postCode": "387 37", "phone": "+420606898595", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 45, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 387 37", "originalData": {"Order Number 1": "1AM09190299", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "Tøebohostice 45", "Recipient Post Code": "387 37", "Recipient City": "Tøebohostice", "Recipient Phone GSM": "+420606898595", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "9", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 121, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 4, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Třešovice, 4", "city": "Třešovice", "postCode": "386 01", "phone": "00420737404306", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 4, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "77B02897110", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Tøešovice, 4", "Recipient Post Code": "386 01", "Recipient City": "Tøešovice", "Recipient Phone GSM": "00420737404306", "Poznámky": "Tel. avizo: 00420737404306", "Poznámky zak servis": "", "Total Weight": "0.68", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 122, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON> 508 / 3, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON><PERSON> 508 / 3", "city": "<PERSON><PERSON><PERSON><PERSON>", "postCode": "389 01", "phone": "+420608107848", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> 508 / 3, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM08547426", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON> 508 / 3", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420608107848", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 123, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON> 10, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Radcice 10", "city": "<PERSON><PERSON><PERSON><PERSON>", "postCode": "389 01", "phone": "+420773977223", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 10, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "99N00257876", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Radcice 10", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420773977223", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.45", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 124, "displayName": "Zdena Kvítková", "personName": "", "googleMapsLabel": "Zdena Kvítková", "companyName": "Zdena Kvítková", "recipientName": "", "surname": "Zdena Kvítková", "address": "<PERSON><PERSON><PERSON><PERSON> 69, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON><PERSON><PERSON> 69", "city": "<PERSON><PERSON><PERSON><PERSON>", "postCode": "389 01", "phone": "+420606157897", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 69, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09191614", "Smìr": "S220602", "DT": "", "Recipient Company Name": "Zdena Kvítková", "Recipient Name": "", "Recipient Surname": "Zdena Kvítková", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON> 69", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420606157897", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 125, "displayName": "<PERSON><PERSON><PERSON>K<PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>K<PERSON>", "companyName": "<PERSON><PERSON><PERSON>K<PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>K<PERSON>", "address": "<PERSON><PERSON><PERSON> 272, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON>rsova 272", "city": "<PERSON><PERSON><PERSON><PERSON>", "postCode": "389 01", "phone": "+420776289757", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 272, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09192202", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>K<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>K<PERSON>", "Recipient Street": "<PERSON>rsova 272", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420776289757", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 126, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON> 135/I, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON> 135/I", "city": "Vodňany", "postCode": "389 01", "phone": "+420608895984", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> 135/I, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM08605268", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON> 135/I", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420608895984", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "8", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 127, "displayName": "Frantisek <PERSON>", "personName": "", "googleMapsLabel": "Frantisek <PERSON>", "companyName": "Frantisek <PERSON>", "recipientName": "", "surname": "Frantisek <PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>  499, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON>  499", "city": "Vodňany", "postCode": "389 01", "phone": "+420604728341", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>  499, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09191265", "Smìr": "S220602", "DT": "", "Recipient Company Name": "Frantisek <PERSON>", "Recipient Name": "", "Recipient Surname": "Frantisek <PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON><PERSON>  499", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420604728341", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 128, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "ČSLA 131, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "ČSLA 131", "city": "Vodňany", "postCode": "389 01", "phone": "+420602409225", "fullGoogleMapsText": "<PERSON><PERSON>, Č<PERSON><PERSON> 131, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM08515112", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "ÈSLA 131", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420602409225", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 129, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1133, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Družstevní 1133", "city": "Vodňany", "postCode": "389 01", "phone": "+420731182207", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1133, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09220451", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Družstevní 1133", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420731182207", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "3.39", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 130, "displayName": "EKOINSTAL CZ s.r.o.", "personName": "<PERSON><PERSON>", "googleMapsLabel": "EKOINSTAL CZ s.r.o. - <PERSON><PERSON>", "companyName": "EKOINSTAL CZ s.r.o.", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON> 55, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Husova 55", "city": "Vodňany", "postCode": "389 01", "phone": "+420602569083", "fullGoogleMapsText": "EKOINSTAL CZ s.r.o. - <PERSON><PERSON>, <PERSON><PERSON><PERSON> 55, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM08397799", "Smìr": "S220602", "DT": "", "Recipient Company Name": "EKOINSTAL CZ s.r.o.", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Husova 55", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420602569083", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "3", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 131, "displayName": "OX BOX Kampanova", "personName": "<PERSON><PERSON><PERSON>", "googleMapsLabel": "OX BOX Kampanova - <PERSON><PERSON><PERSON>", "companyName": "OX BOX Kampanova ", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON>  591, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Kampanova  591", "city": "Vodňany", "postCode": "389 01", "phone": "+420773245501", "fullGoogleMapsText": "OX BOX Kampanova - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>  591, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09213598", "Smìr": "S220602", "DT": "", "Recipient Company Name": "OX BOX Kampanova ", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Kampanova  591", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420773245501", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "10", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 132, "displayName": "Pivoňka Petr", "personName": "", "googleMapsLabel": "Pivoňka Petr", "companyName": "Pivoňka Petr", "recipientName": "", "surname": "Pivoňka Petr", "address": "Křepice 12, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Křepice 12", "city": "Vodňany", "postCode": "389 01", "phone": "+420604759549", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON> Petr, Kř<PERSON><PERSON> 12, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09224730", "Smìr": "S220602", "DT": "", "Recipient Company Name": "Pivoòka Petr", "Recipient Name": "", "Recipient Surname": "Pivoòka Petr", "Recipient Street": "Køepice 12", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420604759549", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 133, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 70, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Křtětice 70", "city": "Vodňany", "postCode": "389 01", "phone": "+420725359245", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 70, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM08427874", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Køtìtice 70", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420725359245", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 134, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "", "address": "Lidmovice 8, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Lidmovice 8", "city": "Vodňany", "postCode": "389 01", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 8, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "24X00977149", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Lidmovice 8", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "4.5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 135, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "<PERSON><PERSON> 1211, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON> 1211", "city": "Vodňany", "postCode": "389 01", "phone": "+420728319057", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON> 1211, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM05378314", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON> 1211", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420728319057", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "30", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "139"}}, {"id": 136, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 958, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON><PERSON>va 958", "city": "Vodňany", "postCode": "389 01", "phone": "+420773916992", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 958, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09210894", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON>va 958", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420773916992", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "2", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 137, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "Palackého 69 38901 / 69, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Palackého 69 38901 / 69", "city": "Vodňany", "postCode": "389 01", "phone": "+420736143463", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 69 38901 / 69, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09208948", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Palackého 69 38901 / 69", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420736143463", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 138, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "<PERSON>", "address": "Radč<PERSON> 26, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Radčice 26", "city": "Vodňany", "postCode": "389 01", "phone": "+420722482769", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON> 26, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM08636890", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Radèice 26", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420722482769", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 139, "displayName": "<PERSON>", "personName": "Šiklová", "googleMapsLabel": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "companyName": "<PERSON>", "recipientName": "<PERSON>", "surname": "Šiklová", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 346, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Radomilická 346", "city": "Vodňany", "postCode": "389 01", "phone": "+420603110285", "fullGoogleMapsText": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 346, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "14I68046933", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "<PERSON>", "Recipient Surname": "Šiklová", "Recipient Street": "Radomilická 346", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420603110285", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "14", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 140, "displayName": "Mateřská škola Vodňany, Smetanova 204", "personName": "", "googleMapsLabel": "Mateřská škola Vodňany, Smetanova 204", "companyName": "Mateřská škola Vodňany, Smetanova 204", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON> 204, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Smetanova 204", "city": "Vodňany", "postCode": "389 01", "phone": "00420606483900", "fullGoogleMapsText": "Mateřsk<PERSON> š<PERSON>la Vodňany, <PERSON><PERSON><PERSON><PERSON> 204, <PERSON><PERSON><PERSON><PERSON> 204, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1ZJ00002916", "Smìr": "S220602", "DT": "", "Recipient Company Name": "Mateøská škola Vodòany, Smetanova 204", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Smetanova 204", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "00420606483900", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1.52", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 141, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 318, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "St<PERSON>žická 318", "city": "Vodňany", "postCode": "389 01", "phone": "+420728549498", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 318, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM08435400", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "St<PERSON>žická 318", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420728549498", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 142, "displayName": "Aranžérská Dílna Honzík", "personName": "<PERSON><PERSON>", "googleMapsLabel": "Aranžérská Dílna Honzík - Lacko", "companyName": "Aranžérská Dílna Honzík", "recipientName": "Jan", "surname": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 690, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "St<PERSON>žická 690", "city": "Vodňany", "postCode": "389 01", "phone": "+420704050866", "fullGoogleMapsText": "Aranžérská Dílna Honzík - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 690, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "00701050248", "Smìr": "S220602", "DT": "CODVM", "Recipient Company Name": "Aranžérská Dílna Honzík", "Recipient Name": "Jan", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "St<PERSON>žická 690", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420704050866", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 143, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1117, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Œumavska 1117", "city": "Vodňany", "postCode": "389 01", "phone": "+420602255200", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1117, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09222228", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Œumavska 1117", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420602255200", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1.4", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 144, "displayName": "<PERSON><PERSON><PERSON>K<PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>K<PERSON>", "companyName": "<PERSON><PERSON><PERSON>K<PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>K<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 272, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "<PERSON><PERSON><PERSON><PERSON> 272", "city": "Vodňany", "postCode": "389 01", "phone": "+420776289757", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 272, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09211944", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>K<PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>K<PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON> 272", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420776289757", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "3", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 145, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "", "address": "Výstavní 1038 1, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Výstavní 1038 1", "city": "Vodňany", "postCode": "389 01", "phone": "+420732150466", "fullGoogleMapsText": "<PERSON><PERSON>, Výstavní 1038 1, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "35O00427496", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Výstavní 1038 1", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420732150466", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 146, "displayName": "CZ001CST", "personName": "<PERSON>", "googleMapsLabel": "CZ001CST - <PERSON>", "companyName": "CZ001CST", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1043, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Výstavní 1043 ", "city": "Vodňany", "postCode": "389 01", "phone": "+420777319465", "fullGoogleMapsText": "CZ001CST - <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1043, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09173218", "Smìr": "S220602", "DT": "", "Recipient Company Name": "CZ001CST", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Výstavní 1043 ", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420777319465", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "9.9", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 147, "displayName": "<PERSON><PERSON><PERSON> - POKR", "personName": "<PERSON><PERSON><PERSON>", "googleMapsLabel": "<PERSON><PERSON><PERSON> - POKR - <PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON> - POKR", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON> 333, VODŇANY, 389 01", "street": "<PERSON><PERSON><PERSON> 333", "city": "VODŇANY", "postCode": "389 01", "phone": "+420733604513", "fullGoogleMapsText": "<PERSON><PERSON><PERSON> - POKR - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 333, VODŇANY, 389 01", "originalData": {"Order Number 1": "1AM09200104", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON> - POKR", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "<PERSON><PERSON><PERSON> 333", "Recipient Post Code": "389 01", "Recipient City": "VODÒANY", "Recipient Phone GSM": "+420733604513", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 148, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON><PERSON>", "address": "Zizkovo naměsti 161, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Zizkovo naměsti 161", "city": "Vodňany", "postCode": "389 01", "phone": "+420724768668", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>mě<PERSON> 161, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09203664", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "Zizkovo namìsti 161", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420724768668", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "19", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 149, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1188, <PERSON><PERSON><PERSON><PERSON> <PERSON>, 389 01", "street": "Bavorovska 1188", "city": "Vodnany II", "postCode": "389 01", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1188, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09221016", "Smìr": "S220602", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Bavorovska 1188", "Recipient Post Code": "389 01", "Recipient City": "Vodnany II", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 150, "displayName": "037 TESCO SM 14031 Vodňany", "personName": "", "googleMapsLabel": "037 TESCO SM 14031 Vodňany", "companyName": "037 TESCO SM 14031 Vodňany", "recipientName": "", "surname": "", "address": "<PERSON>. <PERSON><PERSON> 1278, <PERSON><PERSON><PERSON><PERSON>, Vodňany II, 389 01", "street": "Dr. <PERSON> 1278", "city": "Vodňany, Vodňany II", "postCode": "389 01", "phone": "", "fullGoogleMapsText": "037 TESCO SM 14031 Vodňany, <PERSON><PERSON> <PERSON><PERSON> 1278, <PERSON><PERSON><PERSON><PERSON>, Vodňany II, 389 01", "originalData": {"Order Number 1": "59K00081468", "Smìr": "S220602", "DT": "", "Recipient Company Name": "037 TESCO SM 14031 Vodòany", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Dr. <PERSON> 1278", "Recipient Post Code": "389 01", "Recipient City": "Vodòany, Vodòany II", "Recipient Phone GSM": "", "Poznámky": "16:00 - 18:00\nVym. zas.: Sb<PERSON>rn<PERSON> pošta MS", "Poznámky zak servis": "", "Total Weight": "5", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 151, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON><PERSON>", "recipientName": "", "surname": "", "address": "Štěchovice 89, <PERSON><PERSON><PERSON>, 387 16", "street": "Štěchovice 89", "city": "Volenice", "postCode": "387 16", "phone": "", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 89, <PERSON><PERSON><PERSON>, 387 16", "originalData": {"Order Number 1": "5IL00000305", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Štìchovice 89", "Recipient Post Code": "387 16", "Recipient City": "Volenice", "Recipient Phone GSM": "", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "NE", "Délka doruèení": "0"}}, {"id": 152, "displayName": "PENTA CZ, s.r.o. - PENTA CZ s.r.o.", "personName": "26.05.2025", "googleMapsLabel": "PENTA CZ, s.r.o. - PENTA CZ s.r.o. - 26.05.2025", "companyName": "PENTA CZ, s.r.o. - PENTA CZ s.r.o.", "recipientName": "", "surname": "26.05.2025", "address": "Kosmetick<PERSON> 450, <PERSON><PERSON><PERSON>, 387 11", "street": "Kosmetická 450", "city": "Katovice", "postCode": "387 11", "phone": "+420778885886", "fullGoogleMapsText": "PENTA CZ, s.r.o. - PENTA CZ s.r.o. - 26.05.2025, <PERSON><PERSON><PERSON><PERSON><PERSON> 450, <PERSON><PERSON><PERSON>, 387 11", "originalData": {"Order Number 1": "6F000000383", "Smìr": "S220500", "DT": "P", "Recipient Company Name": "PENTA CZ, s.r.o. - PENTA CZ s.r.o.", "Recipient Name": "", "Recipient Surname": "26.05.2025", "Recipient Street": "Kosmetická 450", "Recipient Post Code": "387 11", "Recipient City": "Katovice", "Recipient Phone GSM": "+420778885886", "Poznámky": "<PERSON>as svozu: Po 11:00-14:00; ", "Poznámky zak servis": "", "Total Weight": "0", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "ANO", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 153, "displayName": "<PERSON><PERSON>", "personName": "Pokorná", "googleMapsLabel": "Monika <PERSON> - Pokorná", "companyName": "<PERSON><PERSON>", "recipientName": "<PERSON><PERSON>", "surname": "Pokorná", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> č.ev. 102, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Dr<PERSON><PERSON><PERSON>ov č.ev. 102", "city": "Strakonice", "postCode": "386 01", "phone": "", "fullGoogleMapsText": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> č.ev. 102, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1Z700586224", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON>", "Recipient Surname": "Pokorná", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON><PERSON> è.ev. 102", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "", "Poznámky": "Textil(Obleèení) - 7772053", "Poznámky zak servis": "", "Total Weight": "1.84", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "ANO", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 154, "displayName": "<PERSON>", "personName": "Roubinová", "googleMapsLabel": "<PERSON> - Rou<PERSON>", "companyName": "<PERSON>", "recipientName": "<PERSON>", "surname": "Roubinová", "address": "<PERSON><PERSON> z Prachové  119, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Kochana z Prachové  119", "city": "Strakonice", "postCode": "386 01", "phone": "", "fullGoogleMapsText": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  119, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "04G00024160", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "<PERSON>", "Recipient Surname": "Roubinová", "Recipient Street": "Kochana z Prachové  119", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "", "Poznámky": "A+<PERSON>\nTextil(Obleèení) - 0605-2521-1050-0405", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "ANO", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 155, "displayName": "Uloženka by WEDO - VM00770001-Pra<PERSON><PERSON><PERSON>", "personName": "26.05.2025", "googleMapsLabel": "Uloženka by WEDO - VM00770001-Praž<PERSON>rna Drah<PERSON> - 26.05.2025", "companyName": "Uloženka by WEDO - VM00770001-Pra<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "26.05.2025", "address": "Drahonic<PERSON> 15, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Drahonice 15", "city": "Vodňany", "postCode": "389 01", "phone": "+420776611242", "fullGoogleMapsText": "Uloženka by WEDO - VM00770001-<PERSON><PERSON><PERSON><PERSON><PERSON> - 26.05.2025, <PERSON><PERSON><PERSON><PERSON> 15, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "14I100634076", "Smìr": "S220602", "DT": "P", "Recipient Company Name": "Uloženka by WEDO - VM00770001-Pra<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "26.05.2025", "Recipient Street": "Drahonice 15", "Recipient Post Code": "389 01", "Recipient City": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Phone GSM": "+420776611242", "Poznámky": "<PERSON>as svozu: Po 07:30-18:00; ", "Poznámky zak servis": "", "Total Weight": "0", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "ANO", "Podano": "ANO", "Délka doruèení": "0"}}]}