const XLSX = require('xlsx');
const fs = require('fs');

// Nastavení kódování pro správné zobrazení češtiny
process.stdout.setEncoding('utf8');

console.log('📊 Extraktor adres s opravou konkrétních chyb kódování');
console.log('====================================================\n');

// Funkce pro opravu konkrétních chyb v kódování
function fixSpecificEncodingErrors(text) {
    if (!text || typeof text !== 'string') return text;
    
    // Mapa konkrétních oprav na základě analýzy dat
    const corrections = {
        // Města a místa
        'Chelèice': 'Chelčice',
        'Libìjovice': 'Libějovice',
        'Støelské': 'Střelské',
        'Tøebohostice': 'Třebohostice',
        'Tøešovice': 'Tře<PERSON>ov<PERSON>',
        'Štìchovice': 'Štěchovice',
        'Vodòany': 'Vodňany',
        'Radomyšl': 'Radomyšl',
        
        // Jména a příjmení
        'Nìmcové': 'Němcové',
        'Nìmcová': 'Němcová',
        'Jiøí': 'Jiří',
        'Jiøi': 'Jiří',
        'Køivanec': 'Křivanec',
        'Šedivý': 'Šedivý',
        'Benešová': 'Benešová',
        'Ryklová': 'Ryklová',
        'Kolářiková': 'Koláříková',
        'Petráková': 'Petráková',
        'Chalušová': 'Chalušová',
        'Koštová': 'Koštová',
        'Smrèka': 'Smrčka',
        'Gütter': 'Gütter',
        'Hynouš': 'Hynouš',
        'Špaèek': 'Špaček',
        'Tošner': 'Tošner',
        'Nìmec': 'Němec',
        'Randakova': 'Randáková',
        'Matìjovska': 'Matějovská',
        'Bìlohlavová': 'Bělohlavová',
        'Èernochova': 'Černochová',
        'Zábranský': 'Zábranský',
        'Cyconová': 'Cyconová',
        'Honzík': 'Honzík',
        'Kuncipál': 'Kuncipál',
        'Nováková': 'Nováková',
        'Kvítková': 'Kvítková',
        'Horváthová': 'Horváthová',
        'Daòhová': 'Daňhová',
        'Èesánek': 'Česánek',
        
        // Ulice a názvy
        'Boženy': 'Boženy',
        'Chmelenskeho': 'Chmelenského',
        'Chmelenského': 'Chmelenského',
        'Kochana z Prachové': 'Kochana z Prachové',
        'Luèní': 'Luční',
        'Mírová': 'Mírová',
        'Písecká': 'Písecká',
        'Radošovice': 'Radošovice',
        'Øepice': 'Řepice',
        'Smidingerova': 'Smidingerova',
        'Stavbařù': 'Stavbařů',
        'Štìkeò': 'Štěkeň',
        'Tržní': 'Tržní',
        'Václavská': 'Václavská',
        'Velká': 'Velká',
        'Žižkova': 'Žižkova',
        'Bezděkovská': 'Bezděkovská',
        'Komenského': 'Komenského',
        'Chelèickeho': 'Chelčického',
        'Radomylsk': 'Radomyšlská',
        'Volyòská': 'Volyňská',
        'Lidická': 'Lidická',
        'Strašice': 'Strašice',
        'Høebenická': 'Číčenická',
        'Èíèenická': 'Číčenická',
        'Stožická': 'Stožická',
        'Èejetice': 'Čejetice',
        'ÈEJETICE': 'ČEJETICE',
        'Èelakovskeho': 'Čelakovského',
        'Èeských': 'Českých',
        'Havlíèkova': 'Havlíčkova',
        'Hranièní': 'Hraniční',
        'Jeronýmova': 'Jeronýmova',
        'Mladìjovice': 'Mladějovice',
        'Mudr.K Hradeckého': 'MUDr. K. Hradeckého',
        'Na Muškách': 'Na Muškách',
        'Nádražní': 'Nádražní',
        'Nebøehovice': 'Nebřehovice',
        'P. Bezruèe': 'P. Bezruče',
        'Pracejovice': 'Pracejovice',
        'Pøesťovice': 'Přešťovice',
        'Ptakovicka': 'Ptákovická',
        'Radomyšlská': 'Radomyšlská',
        'Sídlištì': 'Sídliště',
        'Sousedovice': 'Sousedovice',
        'Šumavská': 'Šumavská',
        'Trzni': 'Tržní',
        'V Raji': 'V Ráji',
        'Vaclavska': 'Václavská',
        'Velka': 'Velká',
        'Zizkova': 'Žižkova',
        
        // Obecné opravy
        'ì': 'ě',
        'ø': 'ř',
        'è': 'č',
        'ò': 'ň',
        'ù': 'ů',
        'Ì': 'Ě',
        'Ø': 'Ř',
        'È': 'Č',
        'Ò': 'Ň',
        'Ù': 'Ů',
        
        // Specifické kombinace
        'PTÁÈEK': 'PTÁČEK',
        'doruèení': 'doručení',
        'Pùvodní': 'Původní',
        'smìr': 'směr'
    };
    
    let result = text;
    
    // Aplikujeme opravy v pořadí od nejspecifičtějších po nejobecnější
    for (const [wrong, correct] of Object.entries(corrections)) {
        if (result.includes(wrong)) {
            result = result.replace(new RegExp(wrong.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), correct);
        }
    }
    
    return result;
}

try {
    // Načtení Excel souboru
    console.log('📂 Načítám soubor souradnice.xls...');
    
    const fileBuffer = fs.readFileSync('souradnice.xls');
    const workbook = XLSX.read(fileBuffer, { 
        type: 'buffer',
        cellText: false,
        cellDates: true,
        raw: true
    });
    
    // Získáme první list
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    console.log(`📋 Název listu: ${sheetName}`);
    
    // Převedeme na JSON
    const data = XLSX.utils.sheet_to_json(worksheet, {
        raw: false,
        defval: '',
        blankrows: false
    });
    
    console.log(`📊 Celkem řádků: ${data.length}`);
    
    console.log('📍 Extrakce a oprava adres...');
    console.log('=====================================');
    
    // Extrakce adres s opravou kódování
    const addresses = [];
    const uniqueAddresses = new Set();
    
    data.forEach((row, index) => {
        // Hledáme sloupce s adresními údaji a opravujeme kódování
        const street = fixSpecificEncodingErrors(row['Recipient Street'] || row['Ulice'] || row['Street'] || '');
        const city = fixSpecificEncodingErrors(row['Recipient City'] || row['Město'] || row['City'] || '');
        const postCode = fixSpecificEncodingErrors(row['Recipient Post Code'] || row['PSČ'] || row['PostCode'] || '');
        const name = fixSpecificEncodingErrors(row['Recipient Name'] || row['Recipient Company Name'] || row['Jméno'] || row['Name'] || `Místo ${index + 1}`);
        
        // Sestavení adresy
        let fullAddress = '';
        if (street && street.toString().trim()) {
            fullAddress += street.toString().trim();
        }
        if (city && city.toString().trim()) {
            if (fullAddress) fullAddress += ', ';
            fullAddress += city.toString().trim();
        }
        if (postCode && postCode.toString().trim()) {
            if (fullAddress) fullAddress += ', ';
            fullAddress += postCode.toString().trim();
        }
        
        // Opravíme kódování celé adresy
        fullAddress = fixSpecificEncodingErrors(fullAddress);
        
        // Přidáme pouze platné a unikátní adresy
        if (fullAddress.length > 10 && !uniqueAddresses.has(fullAddress)) {
            uniqueAddresses.add(fullAddress);
            addresses.push({
                id: addresses.length + 1,
                name: fixSpecificEncodingErrors(name.toString().trim()),
                address: fullAddress,
                street: fixSpecificEncodingErrors(street.toString().trim()),
                city: fixSpecificEncodingErrors(city.toString().trim()),
                postCode: fixSpecificEncodingErrors(postCode.toString().trim())
            });
        }
    });
    
    console.log(`✅ Nalezeno ${addresses.length} unikátních adres\n`);
    
    // Zobrazení prvních 10 adres pro kontrolu
    console.log('📋 Prvních 10 adres (OPRAVENO):');
    console.log('=====================================');
    addresses.slice(0, 10).forEach((addr, index) => {
        console.log(`${index + 1}. ${addr.name}`);
        console.log(`   📍 ${addr.address}`);
        if (index < addresses.length - 1) console.log('');
    });
    
    // Uložení do JSON souboru
    const outputData = {
        totalAddresses: addresses.length,
        extractedAt: new Date().toISOString(),
        encoding: 'Opraveno - specifické chyby kódování',
        corrections: 'Chelčice, Němcové, Jiří, Libějovice, atd.',
        addresses: addresses
    };
    
    fs.writeFileSync('extracted-addresses-corrected.json', JSON.stringify(outputData, null, 2), { encoding: 'utf8' });
    console.log('\n💾 Adresy uloženy do souboru: extracted-addresses-corrected.json');
    
    // Vytvoření jednoduchého seznamu
    const simpleList = addresses.map(addr => addr.address).join('\n');
    fs.writeFileSync('addresses-list-corrected.txt', simpleList, { encoding: 'utf8' });
    console.log('📝 Jednoduchý seznam uložen do: addresses-list-corrected.txt');
    
    // Vytvoření seznamu s názvy
    const detailedList = addresses.map((addr, index) => 
        `${index + 1}. ${addr.name}\n   📍 ${addr.address}`
    ).join('\n\n');
    fs.writeFileSync('addresses-detailed-corrected.txt', detailedList, { encoding: 'utf8' });
    console.log('📋 Detailní seznam uložen do: addresses-detailed-corrected.txt');
    
    // Kontrola oprav
    console.log('\n🔍 Kontrola provedených oprav:');
    console.log('=====================================');
    
    const checkPatterns = ['Chelčice', 'Němcové', 'Jiří', 'Libějovice', 'Střelské', 'Třebohostice'];
    checkPatterns.forEach(pattern => {
        const count = addresses.filter(addr => 
            addr.address.includes(pattern) || addr.name.includes(pattern)
        ).length;
        if (count > 0) {
            console.log(`✅ ${pattern}: ${count}x`);
        }
    });
    
    // Kontrola zbývajících problémů
    const stillProblematic = addresses.filter(addr => 
        addr.address.includes('ì') || addr.address.includes('ø') || addr.address.includes('è') ||
        addr.name.includes('ì') || addr.name.includes('ø') || addr.name.includes('è')
    );
    
    if (stillProblematic.length > 0) {
        console.log('\n⚠️ Stále problematické adresy:');
        stillProblematic.forEach((addr, index) => {
            console.log(`${index + 1}. ${addr.name} - ${addr.address}`);
        });
    } else {
        console.log('\n✅ Všechny známé problémy s kódováním opraveny!');
    }
    
    console.log('\n🎯 HOTOVO!');
    console.log('=====================================');
    console.log('Vytvořené soubory s opravenými adresami:');
    console.log('• extracted-addresses-corrected.json - všechny adresy');
    console.log('• addresses-list-corrected.txt - jednoduchý seznam');
    console.log('• addresses-detailed-corrected.txt - detailní seznam');
    console.log('\nNyní můžete:');
    console.log('1. Zkontrolovat opravené adresy');
    console.log('2. Spustit: node group-addresses.js pro seskupení');
    console.log('3. Použít opravené adresy pro plánování tras');
    
} catch (error) {
    console.error('❌ Chyba při zpracování:', error.message);
    console.log('\n💡 Zkontrolujte, že soubor souradnice.xls existuje');
}
