{"routes": [{"id": 1, "name": "Google Maps Trasa 1", "addressCount": 8, "addresses": [{"id": 1, "displayName": "OX BOX Chelčice", "personName": "olga nová", "googleMapsLabel": "OX BOX Chelčice - olga nová", "companyName": "OX BOX Chelčice", "recipientName": "", "surname": "olga nová", "address": "Chelčice 5, <PERSON><PERSON><PERSON><PERSON>, 389 01", "street": "Chelčice 5", "city": "Chelčice", "postCode": "389 01", "phone": "+420777862314", "fullGoogleMapsText": "OX BOX Chelčice - olga nová, Chelčice 5, <PERSON><PERSON><PERSON><PERSON>, 389 01", "originalData": {"Order Number 1": "1AM09191103", "Smìr": "S220602", "DT": "", "Recipient Company Name": "OX BOX Chelèice", "Recipient Name": "", "Recipient Surname": "olga nová", "Recipient Street": "Chelèice 5", "Recipient Post Code": "389 01", "Recipient City": "Chelèice", "Recipient Phone GSM": "+420777862314", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220602", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 2, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON> 55, <PERSON><PERSON>, 386 01", "street": "<PERSON>ín 55", "city": "<PERSON><PERSON>", "postCode": "386 01", "phone": "+420722960264", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> 55, <PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09186548", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "<PERSON>ín 55", "Recipient Post Code": "386 01", "Recipient City": "<PERSON><PERSON>", "Recipient Phone GSM": "+420722960264", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 3, "displayName": "Pazdera Jiří", "personName": "<PERSON><PERSON><PERSON>", "googleMapsLabel": "Pazdera Jiří - <PERSON><PERSON><PERSON>", "companyName": "Pazdera Jiří", "recipientName": "<PERSON><PERSON><PERSON>", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 84, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 72", "street": "Libějovice 84", "city": "Libějovice", "postCode": "387 72", "phone": "00420605297185", "fullGoogleMapsText": "Pazdera Jiří - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 84, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 72", "originalData": {"Order Number 1": "87T00071032", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON><PERSON>", "Recipient Surname": "", "Recipient Street": "Libìjovice 84", "Recipient Post Code": "387 72", "Recipient City": "Libìjovice", "Recipient Phone GSM": "00420605297185", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "1", "Box count": "1", "Pùvodní smìr": "S440700", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 4, "displayName": "<PERSON><PERSON><PERSON>", "personName": "<PERSON><PERSON>", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON>", "address": "46, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "46", "city": "Strakonice", "postCode": "386 01", "phone": "+420723539104", "fullGoogleMapsText": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, 46, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "45T00088031", "Smìr": "8224614", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON><PERSON>", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "46", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420723539104", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "50", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "251"}}, {"id": 5, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "personName": "Hálová", "googleMapsLabel": "<PERSON><PERSON><PERSON><PERSON>lov<PERSON>", "companyName": "<PERSON><PERSON><PERSON><PERSON>", "recipientName": "Drah<PERSON><PERSON>", "surname": "Hálová", "address": "<PERSON><PERSON><PERSON><PERSON> 1398, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Bahenní 1398", "city": "Strakonice", "postCode": "386 01", "phone": "*********", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1398, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1H200001844", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Name": "Drah<PERSON><PERSON>", "Recipient Surname": "Hálová", "Recipient Street": "Bahenní 1398", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "*********", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "23", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 6, "displayName": "<PERSON>", "personName": "Ho<PERSON>ová", "googleMapsLabel": "<PERSON> - Ho<PERSON>ová", "companyName": "<PERSON>", "recipientName": "<PERSON>", "surname": "Ho<PERSON>ová", "address": "<PERSON><PERSON><PERSON><PERSON> Ně<PERSON>v<PERSON> 1117, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Boženy Němcové 1117", "city": "Strakonice", "postCode": "386 01", "phone": "+420606636880", "fullGoogleMapsText": "<PERSON> - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Ně<PERSON> 1117, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "09M00019605", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "<PERSON>", "Recipient Surname": "Ho<PERSON>ová", "Recipient Street": "Boženy Nìmcové 1117", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420606636880", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 7, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Dražejov90", "city": "Strakonice", "postCode": "386 01", "phone": "+420734357069", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1X820354950", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Dražejov90", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420734357069", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.03", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 8, "displayName": "CZ004CST", "personName": "<PERSON><PERSON>", "googleMapsLabel": "CZ004CST - <PERSON><PERSON>", "companyName": "CZ004CST", "recipientName": "", "surname": "<PERSON><PERSON>", "address": "Dukels<PERSON>á 33, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Dukelská 33 ", "city": "Strakonice", "postCode": "386 01", "phone": "+420607065695", "fullGoogleMapsText": "CZ004CST - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 33, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09184140", "Smìr": "S220500", "DT": "", "Recipient Company Name": "CZ004CST", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Dukelská 33 ", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420607065695", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}], "googleMapsTexts": ["OX BOX Chelčice - olga nová, Chelčice 5, <PERSON><PERSON><PERSON><PERSON>, 389 01", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> 55, <PERSON><PERSON>, 386 01", "Pazdera Jiří - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 84, <PERSON><PERSON><PERSON><PERSON><PERSON>, 387 72", "<PERSON><PERSON><PERSON> <PERSON><PERSON>, 46, <PERSON><PERSON><PERSON><PERSON>, 386 01", "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1398, <PERSON><PERSON><PERSON><PERSON>, 386 01", "<PERSON> - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Ně<PERSON> 1117, <PERSON><PERSON><PERSON><PERSON>, 386 01", "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 386 01", "CZ004CST - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 33, <PERSON><PERSON><PERSON><PERSON>, 386 01"]}, {"id": 2, "name": "Google Maps Trasa 2", "addressCount": 8, "addresses": [{"id": 9, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 167, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 167", "city": "Strakonice", "postCode": "386 01", "phone": "00420734511135", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 167, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "77B03602338", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 167", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "00420734511135", "Poznámky": "Tel. avizo: 00420734511135", "Poznámky zak servis": "", "Total Weight": "20.36", "Total Volume": "0.118", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 10, "displayName": "PTÁČEK - velkoobchod, a.s.", "personName": "", "googleMapsLabel": "PTÁČEK - velkoobchod, a.s.", "companyName": "PTÁČEK - velkoobchod, a.s.", "recipientName": "", "surname": "", "address": "Katovick<PERSON> 1268 - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Katovická 1268 - IC", "city": "Strakonice", "postCode": "386 01", "phone": "", "fullGoogleMapsText": "PTÁČEK - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a.s., Katovická 1268 - IC, Strak<PERSON>e, 386 01", "originalData": {"Order Number 1": "2X700046063", "Smìr": "S220500", "DT": "", "Recipient Company Name": "PTÁÈEK - velkoobchod, a.s.", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Katovická 1268 - IC", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "2.13", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 11, "displayName": "<PERSON><PERSON><PERSON>", "personName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "googleMapsLabel": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1307, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Katovická 1307", "city": "Strakonice", "postCode": "386 01", "phone": "+420606843920", "fullGoogleMapsText": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1307, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "B1200169929", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "<PERSON><PERSON><PERSON>", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "Katovická 1307", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420606843920", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "19", "Total Volume": "0", "Box count": "2", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 12, "displayName": "AlzaBox Katovická", "personName": "Lenka Slavíčková", "googleMapsLabel": "AlzaBox Katovická - Lenka Slavíčková", "companyName": "AlzaBox Katovická", "recipientName": "", "surname": "Lenka Slavíčková", "address": "<PERSON><PERSON><PERSON><PERSON> 1404, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Katovická 1404", "city": "Strakonice", "postCode": "386 01", "phone": "420739755746", "fullGoogleMapsText": "AlzaBox Katovická - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1404, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09187240", "Smìr": "S220500", "DT": "", "Recipient Company Name": "AlzaBox Katovická", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON>", "Recipient Street": "Katovická 1404", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "420739755746", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 13, "displayName": "<PERSON><PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON><PERSON>", "recipientName": "", "surname": "", "address": "Kochana z Prachové119, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Kochana z Prachové119", "city": "Strakonice", "postCode": "386 01", "phone": "+420722596652", "fullGoogleMapsText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>119, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1X820354917", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Kochana z Prachové119", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420722596652", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.03", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 14, "displayName": "¼uboš Bandry", "personName": "", "googleMapsLabel": "¼uboš Bandry", "companyName": "¼uboš Bandry", "recipientName": "¼uboš Bandry", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON> 448, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Luční 448", "city": "Strakonice", "postCode": "386 01", "phone": "+420723785332", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 448, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "7K700104747", "Smìr": "S220500", "DT": "", "Recipient Company Name": "¼uboš Bandry", "Recipient Name": "¼uboš Bandry", "Recipient Surname": "", "Recipient Street": "Luèní 448", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420723785332", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "30", "Total Volume": "0", "Box count": "2", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 15, "displayName": "CZ003CST", "personName": "<PERSON>", "googleMapsLabel": "CZ003CST - <PERSON>", "companyName": "CZ003CST", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 1010, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Mírová 1010 ", "city": "Strakonice", "postCode": "386 01", "phone": "+420606732965", "fullGoogleMapsText": "CZ003CST - <PERSON>, <PERSON><PERSON><PERSON><PERSON> 1010, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09188884", "Smìr": "S220500", "DT": "", "Recipient Company Name": "CZ003CST", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Mírová 1010 ", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420606732965", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 16, "displayName": "AlzaBox Na Ohradě", "personName": "<PERSON>", "googleMapsLabel": "AlzaBox Na Ohradě - <PERSON>", "companyName": "AlzaBox Na Ohradě", "recipientName": "", "surname": "<PERSON>", "address": "<PERSON> Ohradě 2, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Na Ohradě 2", "city": "Strakonice", "postCode": "386 01", "phone": "+420607783283", "fullGoogleMapsText": "AlzaBox Na Ohradě - <PERSON>, <PERSON> 2, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09184371", "Smìr": "S220500", "DT": "", "Recipient Company Name": "AlzaBox Na Ohradì", "Recipient Name": "", "Recipient Surname": "<PERSON>", "Recipient Street": "Na Ohradì 2", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420607783283", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}], "googleMapsTexts": ["<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 167, <PERSON><PERSON><PERSON><PERSON>, 386 01", "PTÁČEK - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a.s., Katovická 1268 - IC, Strak<PERSON>e, 386 01", "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1307, <PERSON><PERSON><PERSON><PERSON>, 386 01", "AlzaBox Katovická - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1404, <PERSON><PERSON><PERSON><PERSON>, 386 01", "<PERSON><PERSON><PERSON>, <PERSON><PERSON>119, <PERSON><PERSON><PERSON><PERSON>, 386 01", "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 448, <PERSON><PERSON><PERSON><PERSON>, 386 01", "CZ003CST - <PERSON>, <PERSON><PERSON><PERSON><PERSON> 1010, <PERSON><PERSON><PERSON><PERSON>, 386 01", "AlzaBox Na Ohradě - <PERSON>, <PERSON> 2, <PERSON><PERSON><PERSON><PERSON>, 386 01"]}, {"id": 3, "name": "Google Maps Trasa 3", "addressCount": 8, "addresses": [{"id": 17, "displayName": "AlzaBox Písecká", "personName": "<PERSON><PERSON><PERSON><PERSON>", "googleMapsLabel": "AlzaBox Písecká - <PERSON><PERSON><PERSON><PERSON>", "companyName": "AlzaBox Písecká", "recipientName": "", "surname": "<PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON>ecká, Strakonice, 386 01", "street": "Písecká ", "city": "Strakonice", "postCode": "386 01", "phone": "+420774343499", "fullGoogleMapsText": "AlzaBox Písecká - <PERSON><PERSON><PERSON><PERSON>, P<PERSON>eck<PERSON>, Strakonice, 386 01", "originalData": {"Order Number 1": "1AM09187851", "Smìr": "S220500", "DT": "", "Recipient Company Name": "AlzaBox Písecká", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "Písecká ", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420774343499", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 18, "displayName": "<PERSON>", "personName": "<PERSON><PERSON><PERSON><PERSON>", "googleMapsLabel": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "companyName": "<PERSON>", "recipientName": "<PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "address": "Radošovice  42, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Radošovice  42", "city": "Strakonice", "postCode": "386 01", "phone": "", "fullGoogleMapsText": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>  42, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "04G00017808", "Smìr": "8224614", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "<PERSON>", "Recipient Surname": "<PERSON><PERSON><PERSON><PERSON>", "Recipient Street": "Radošovice  42", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "", "Poznámky": "Textil(Obleèení) - 0605-2430-1022-5890", "Poznámky zak servis": "<PERSON><PERSON><PERSON><PERSON>, opìtovné vyzvednutí 31.07.2024", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "204"}}, {"id": 19, "displayName": "ELEKTRO S.M.S., spol. s r.o.Strakonice", "personName": "", "googleMapsLabel": "ELEKTRO S.M.S., spol. s r.o.Strakonice", "companyName": "ELEKTRO S.M.S., spol. s r.o.Strakonice", "recipientName": "", "surname": "", "address": "Řepice 136, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Řepice 136", "city": "Strakonice", "postCode": "386 01", "phone": "+420383322474", "fullGoogleMapsText": "ELEKTRO S.M.S., spol. s r.<PERSON><PERSON>, Řepice 136, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "4L500011661", "Smìr": "S220500", "DT": "", "Recipient Company Name": "ELEKTRO S.M.S., spol. s r.o.Strakonice", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Øepice 136", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420383322474", "Poznámky": "", "Poznámky zak servis": "Smìrovaní zásilky zpìt", "Total Weight": "10", "Total Volume": "0.085", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "1"}}, {"id": 20, "displayName": "AlzaBox Smetanova", "personName": "<PERSON><PERSON><PERSON>", "googleMapsLabel": "AlzaBox Smetanova - <PERSON><PERSON><PERSON>", "companyName": "AlzaBox Smetanova", "recipientName": "", "surname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON><PERSON> 883, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Smetanova 883", "city": "Strakonice", "postCode": "386 01", "phone": "+420736162719", "fullGoogleMapsText": "AlzaBox Smetanova - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 883, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1AM09194315", "Smìr": "S220500", "DT": "", "Recipient Company Name": "AlzaBox Smetanova", "Recipient Name": "", "Recipient Surname": "<PERSON><PERSON><PERSON>", "Recipient Street": "Smetanova 883", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420736162719", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "1", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "-1"}}, {"id": 21, "displayName": "Ondř<PERSON>,", "personName": "", "googleMapsLabel": "Ondř<PERSON>,", "companyName": "Ondř<PERSON>,", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> 795, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Smidingerova 795", "city": "Strakonice", "postCode": "386 01", "phone": "+420722727888", "fullGoogleMapsText": "<PERSON><PERSON><PERSON><PERSON>,, <PERSON><PERSON><PERSON><PERSON><PERSON> 795, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "4RA00063572", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON><PERSON><PERSON>,", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Smidingerova 795", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420722727888", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "2", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 22, "displayName": "<PERSON>", "personName": "", "googleMapsLabel": "<PERSON>", "companyName": "<PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>207, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Stavbařů207", "city": "Strakonice", "postCode": "386 01", "phone": "+420605260940", "fullGoogleMapsText": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>207, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1X820354902", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Stavbaøù207", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420605260940", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.03", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 23, "displayName": "<PERSON>,", "personName": "", "googleMapsLabel": "<PERSON>,", "companyName": "<PERSON>,", "recipientName": "", "surname": "", "address": "Štěkeň-<PERSON><PERSON><PERSON> 194, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Štěkeň-Rechle 194", "city": "Strakonice", "postCode": "386 01", "phone": "+420606748986", "fullGoogleMapsText": "<PERSON>,, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 194, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "4RA00065338", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON>,", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Štìkeò-<PERSON><PERSON><PERSON> 194", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420606748986", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "9.7", "Total Volume": "0.028", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}, {"id": 24, "displayName": "<PERSON><PERSON>", "personName": "", "googleMapsLabel": "<PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "recipientName": "", "surname": "", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>1152, <PERSON><PERSON><PERSON><PERSON>, 386 01", "street": "Tržní1152", "city": "Strakonice", "postCode": "386 01", "phone": "+420604302441", "fullGoogleMapsText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>1152, <PERSON><PERSON><PERSON><PERSON>, 386 01", "originalData": {"Order Number 1": "1X820354886", "Smìr": "S220500", "DT": "", "Recipient Company Name": "<PERSON><PERSON>", "Recipient Name": "", "Recipient Surname": "", "Recipient Street": "Tržní1152", "Recipient Post Code": "386 01", "Recipient City": "Strakonice", "Recipient Phone GSM": "+420604302441", "Poznámky": "", "Poznámky zak servis": "", "Total Weight": "0.4", "Total Volume": "0", "Box count": "1", "Pùvodní smìr": "S220500", "Pick-up": "NE", "Podano": "ANO", "Délka doruèení": "0"}}], "googleMapsTexts": ["AlzaBox Písecká - <PERSON><PERSON><PERSON><PERSON>, P<PERSON>eck<PERSON>, Strakonice, 386 01", "<PERSON> - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>  42, <PERSON><PERSON><PERSON><PERSON>, 386 01", "ELEKTRO S.M.S., spol. s r.<PERSON><PERSON>, Řepice 136, <PERSON><PERSON><PERSON><PERSON>, 386 01", "AlzaBox Smetanova - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 883, <PERSON><PERSON><PERSON><PERSON>, 386 01", "<PERSON><PERSON><PERSON><PERSON>,, <PERSON><PERSON><PERSON><PERSON><PERSON> 795, <PERSON><PERSON><PERSON><PERSON>, 386 01", "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>207, <PERSON><PERSON><PERSON><PERSON>, 386 01", "<PERSON>,, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 194, <PERSON><PERSON><PERSON><PERSON>, 386 01", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>1152, <PERSON><PERSON><PERSON><PERSON>, 386 01"]}]}