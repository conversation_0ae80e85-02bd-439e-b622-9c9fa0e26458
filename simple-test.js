const { chromium } = require('playwright');

(async () => {
    console.log('🧪 Jednoduchý test - 3 adresy + optimalizace');
    console.log('=============================================\n');

    const testAddresses = [
        '46, Strakonice, 386 01',
        'Bahenní 1398, Strakonice, 386 01',
        'Boženy Nímcové 1117, Strakonice, 386 01'
    ];

    console.log('📍 Testovací adresy:');
    testAddresses.forEach((addr, i) => {
        console.log(`   ${i + 1}. ${addr}`);
    });

    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 500
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        console.log('\n🌐 Otevírám mapy.cz...');
        await page.goto('https://mapy.cz', { waitUntil: 'networkidle' });
        await page.waitForTimeout(2000);
        
        // Zavřeme cookies
        try {
            await page.locator('button:has-text("Souhlasím")').click();
            console.log('✅ Cookies přijaty');
        } catch (e) {}
        
        // Aktivujeme plánovač tras
        console.log('🗺️ Aktivuji plánovač tras...');
        await page.locator('button:has-text("Trasa")').click();
        await page.waitForTimeout(2000);
        console.log('✅ Plánovač tras aktivován');
        
        // Přidáme adresy
        console.log('\n📍 Přidávám adresy...');
        for (let i = 0; i < testAddresses.length; i++) {
            const addr = testAddresses[i];
            console.log(`   ${i + 1}. Přidávám: ${addr}`);
            
            // Najdeme input pole
            const inputs = page.locator('input[type="text"]');
            const count = await inputs.count();
            console.log(`      Dostupných inputů: ${count}`);
            
            if (i < count) {
                const input = inputs.nth(i);
                await input.click();
                await input.fill(addr);
                await page.keyboard.press('Enter');
                await page.waitForTimeout(2000);
                console.log(`      ✅ Přidáno`);
            } else {
                console.log(`      ❌ Nedostatek input polí`);
            }
        }
        
        console.log('\n🔍 Hledám optimalizaci trasy...');
        await page.waitForTimeout(3000);
        
        // Hledáme všechna tlačítka a vypisujeme jejich texty
        const allButtons = page.locator('button:visible');
        const buttonCount = await allButtons.count();
        console.log(`📊 Celkem viditelných tlačítek: ${buttonCount}`);
        
        for (let i = 0; i < Math.min(buttonCount, 30); i++) {
            try {
                const button = allButtons.nth(i);
                const text = await button.textContent();
                const title = await button.getAttribute('title');
                const className = await button.getAttribute('class');
                
                if (text && text.trim()) {
                    console.log(`   ${i}: "${text.trim()}" (title: "${title}", class: "${className}")`);
                    
                    // Hledáme optimalizační tlačítka
                    if (text.toLowerCase().includes('optimalizac') ||
                        text.toLowerCase().includes('nejkratší') ||
                        text.toLowerCase().includes('nejrychlejší') ||
                        (title && (title.toLowerCase().includes('optimalizac') ||
                                  title.toLowerCase().includes('nejkratší')))) {
                        console.log(`      🎯 NALEZENO OPTIMALIZAČNÍ TLAČÍTKO!`);
                        try {
                            await button.click();
                            console.log(`      ✅ Optimalizace aktivována!`);
                            await page.waitForTimeout(3000);
                            break;
                        } catch (e) {
                            console.log(`      ❌ Chyba při klikání: ${e.message}`);
                        }
                    }
                }
            } catch (e) {
                continue;
            }
        }
        
        console.log('\n🎯 Test dokončen!');
        console.log('📊 Zkontrolujte výsledek v prohlížeči.');
        console.log('⏳ Prohlížeč zůstane otevřený...');
        
        // Čekáme
        await new Promise(() => {});
        
    } catch (error) {
        console.error('❌ Chyba:', error.message);
    } finally {
        // await browser.close();
    }
})();
